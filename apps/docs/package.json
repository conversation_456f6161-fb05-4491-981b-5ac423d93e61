{"name": "docs", "version": "0.0.0", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "dev": "docusaurus start", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "typecheck": "tsc"}, "dependencies": {"@apollo/ui": "workspace:*", "@codesandbox/sandpack-react": "^2.20.0", "@design-systems/apollo-ui": "workspace:*", "@design-systems/apollo-icons": "workspace:*", "@design-systems/tokens": "workspace:*", "@docusaurus/babel": "^3.8.1", "@docusaurus/core": "3.8.1", "@docusaurus/faster": "^3.8.1", "@docusaurus/plugin-google-gtag": "^3.8.1", "@docusaurus/plugin-sitemap": "^3.8.1", "@docusaurus/preset-classic": "3.8.1", "@docusaurus/theme-common": "^3.8.1", "@docusaurus/theme-live-codeblock": "^3.8.1", "@docusaurus/theme-mermaid": "^3.8.1", "@mdx-js/react": "^3.0.0", "@orama/plugin-docusaurus-v3": "^3.1.6", "classnames": "2.5.1", "clsx": "^2.0.0", "prism-react-renderer": "^2.3.0", "raw-loader": "^4.0.2", "react": "^19.0.0", "react-compiler-runtime": "19.0.0-beta-714736e-20250131", "react-dom": "^19.0.0", "react-live": "^4.1.6"}, "devDependencies": {"@docusaurus/module-type-aliases": "3.8.1", "@docusaurus/tsconfig": "3.8.1", "@docusaurus/types": "3.8.1", "typescript": "~5.6.2"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 3 chrome version", "last 3 firefox version", "last 5 safari version"]}, "engines": {"node": ">=18.0"}}