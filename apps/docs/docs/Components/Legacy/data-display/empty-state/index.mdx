---
title: Empty State
id: legacy:component:empty-state
slug: /legacy/components/empty-state
---

import { EmptyState } from "@design-systems/apollo-ui"

```tsx
import { EmptyState } from "@design-systems/apollo-ui"
```

The `EmptyState` component is used to communicate that there is no content to display in a particular area or state of the application.

```jsx live
function MyComponent() {
  return (
    <EmptyState
      name="AddInformation"
      title="No Data Available"
      description="There is currently no data to display in this section."
    />
  )
}
```

## Props

### EmptyState

| Name          | Type              | Required | Default | Description                                                                                   |
| ------------- | ----------------- | -------- | ------- | --------------------------------------------------------------------------------------------- |
| `title`       | `string`          |          | -       | The main heading text to display.                                                             |
| `description` | `string`          |          | -       | Additional descriptive text below the title.                                                  |
| `name`        | `string`          |          | -       | Name of the built-in illustration to display. Available: `"AddInformation"`, `"FileNotFound"` |
| `actions`     | `React.ReactNode` |          | -       | Action button or element to display below the description.                                    |
| `slots`       | `object`          |          | -       | Object to customize component slots (advanced usage).                                         |

### SlotProps

The `slots` prop allows you to customize different parts of the component. Each slot can receive custom props including `className`, `style`, and custom `render` functions.

| Slot Name             | Description                                                                                    |
| --------------------- | ---------------------------------------------------------------------------------------------- |
| `container`           | Customizes the root container element that wraps the entire component.                         |
| `illustrationWrapper` | Customizes the wrapper element around the illustration.                                        |
| `illustration`        | Customizes the illustration/icon element itself.                                               |
| `contentWrapper`      | Customizes the wrapper element around the title and description content.                       |
| `title`               | Customizes the title element. Receives `title` prop and supports custom rendering.             |
| `description`         | Customizes the description element. Receives `description` prop and supports custom rendering. |
| `actionWrapper`       | Customizes the wrapper element around the action buttons/elements.                             |

Each slot supports:

- **className**: Additional CSS classes to apply
- **style**: Inline styles object
- **render**: Custom render function for complete control over the slot's appearance

## Examples

### Basic Empty State

A simple empty state with title and description.

```jsx live
function MyComponent() {
  return (
    <EmptyState
      title="No items found"
      description="There are no items to display at the moment."
    />
  )
}
```

### Empty State with Action

Include an action button to help users take the next step.

```jsx live
function MyComponent() {
  return (
    <EmptyState
      name="FileNotFound"
      title="No files uploaded"
      description="Upload your first file to get started with your project."
      actions={
        <Button
          style={{
            width: "100%",
          }}
        >
          Upload File
        </Button>
      }
    />
  )
}
```

### Empty State with Built-in Illustration

Use the AddInformation illustration for data entry scenarios.

```jsx live
function MyComponent() {
  return (
    <EmptyState
      name="AddInformation"
      title="No data available"
      description="Start adding information to see your data here."
    />
  )
}
```

### Empty State with File Not Found

Use the FileNotFound illustration for search or missing content scenarios.

```jsx live
function MyComponent() {
  return (
    <EmptyState
      name="FileNotFound"
      title="No results found"
      description="Try adjusting your search or filter criteria to find what you're looking for."
    />
  )
}
```

## Advanced Usage

### Customizing with Slot Props

The `slots` prop allows you to customize the styling and behavior of different parts of the component. Each slot can receive custom className or even custom render functions.

```jsx live
function MyComponent() {
  return (
    <EmptyState
      name="AddInformation"
      title="Custom Styled Empty State"
      description="This example shows how to customize the component using slot props."
      actions={<Button fullWidth>Get Started</Button>}
      slots={{
        title: {
          style: {
            color: "red",
          },
        },
        description: {
          style: {
            color: "green",
          },
        },
      }}
    />
  )
}
```

### Custom Render Functions

You can also provide custom render functions for complete control over how each slot is rendered:

```jsx live
function MyComponent() {
  return (
    <EmptyState
      name="FileNotFound"
      title="Advanced Custom Rendering"
      description="This shows custom render functions for slots."
      actions={<Button fullWidth>Get Started</Button>}
      slots={{
        title: {
          render: ({ title, className }) => (
            <h2
              className={`${className} text-2xl font-extrabold text-red-500 uppercase tracking-wide`}
            >
              🚨 {title}
            </h2>
          ),
        },
        contentWrapper: {
          render: ({ className, children }) => (
            <div
              className={`${className} bg-yellow-50 p-4 rounded-md border-l-4 border-yellow-400`}
            >
              {children}
            </div>
          ),
        },
      }}
    />
  )
}
```

## CSS

| Class Name                              | Description                                        |
| --------------------------------------- | -------------------------------------------------- |
| `.ApolloEmptyState-container`           | Styles applied to the root container element       |
| `.ApolloEmptyState-illustrationWrapper` | Styles applied to the illustration wrapper element |
| `.ApolloEmptyState-illustration`        | Styles applied to the illustration element         |
| `.ApolloEmptyState-contentWrapper`      | Styles applied to the content wrapper element      |
| `.ApolloEmptyState-title`               | Styles applied to the title element                |
| `.ApolloEmptyState-description`         | Styles applied to the description element          |
| `.ApolloEmptyState-actionWrapper`       | Styles applied to the action wrapper element       |
