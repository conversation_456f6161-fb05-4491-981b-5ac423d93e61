---
title: Checkbox
id: legacy:component:checkbox
slug: /legacy/components/checkbox
---

import * as React from "react"
import { Checkbox } from "@design-systems/apollo-ui"

```tsx
import * as React from "react"
import { Checkbox } from "@design-systems/apollo-ui"
```

Displays a checkbox component.

```tsx live
function CheckboxDemo() {
  const [checked, setChecked] = React.useState([true, false])

  const handleChange1 = (event: React.ChangeEvent<HTMLInputElement>) => {
    setChecked([event.target.checked, event.target.checked])
  }

  const handleChange2 = (event: React.ChangeEvent<HTMLInputElement>) => {
    setChecked([event.target.checked, checked[1]])
  }

  const handleChange3 = (event: React.ChangeEvent<HTMLInputElement>) => {
    setChecked([checked[0], event.target.checked])
  }

  const children = (
    <div className="pl-4">
      <Checkbox checked={checked[0]} onChange={handleChange2} label="child-1" />
      <Checkbox checked={checked[1]} onChange={handleChange3} label="child-2" />
    </div>
  )
  return (
    <div>
      <Checkbox
        checked={checked[0] && checked[1]}
        indeterminate={checked[0] !== checked[1]}
        onChange={handleChange1}
        label="parent"
      />
      {children}
    </div>
  )
}
```

## Props

| Name                 | Type      | Default | required |
| -------------------- | --------- | ------- | -------- |
| **`label`**          | `string`  | `none`  | no       |
| **`labelPlacement`** | `string`  | `right` | no       |
| **`indeterminate`**  | `boolean` | `none`  | no       |

## **CSS classes**

| Class Name              | Rule name | Description                 |
| ----------------------- | --------- | --------------------------- |
| `.ApolloCheckbox-root`  | Root      | Styles applied to checkbox  |
| `.ApolloCehckbox-label` | Label     | Styles applied to the label |
