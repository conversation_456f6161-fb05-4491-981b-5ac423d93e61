---
title: Float Button
id: legacy:component:float-button
slug: /legacy/components/float-button
---

import * as React from "react"
import { Smile } from "@design-systems/apollo-icons"
import { FloatButton, useScrollDirection } from "@design-systems/apollo-ui"

```tsx
import * as React from "react"
import { Smile } from "@design-systems/apollo-icons"
import { FloatButton, useScrollDirection } from "@design-systems/apollo-ui"
```

The `Float Button` component is a control button designed to float on the screen. It is typically circular or round in shape and is often brightly colored to ensure it is easily noticeable by users.

```tsx live
function FloatButtonDemo() {
  const [containerRef, setContainerRef] = React.useState<HTMLDivElement | null>(null)
  const { scrollDirection } = useScrollDirection(containerRef)
  const isExpanded = React.useMemo(
    () => (scrollDirection ? ["up", "none"].includes(scrollDirection) : true),
    [scrollDirection]
  )
  return (
    <div style={{ position: "relative", width: "320px" }}>
      <div
        style={{
          width: "100%",
          height: "500px",
          backgroundColor: "grey",
          overflow: "auto",
        }}
        ref={setContainerRef}
      >
        <div
          style={{ width: "100%", height: "1000px", backgroundColor: "lightcoral" }}
        />
      </div>
      <div style={{ position: "absolute", bottom: "64px", left: "12px" }}>
        <FloatButton
          icon={<Smile />}
          iconSide="end"
          label="Help"
          isExpanded={isExpanded}
        />
      </div>
      <div style={{ position: "absolute", top: "64px", right: "12px" }}>
        <FloatButton
          icon={<Smile />}
          iconSide="start"
          label="Help"
          isExpanded={isExpanded}
        />
      </div>
    </div>
  )
}
```

# Props

> This component extends the type from `ButtonProps` which is prop of [Button](https://cjexpress.pages-gitlab.cjexpress.io/design-systems/apollo/docs/components/button) component

| Name             | Type               | Required | Default   | Description                                         |
| ---------------- | ------------------ | -------- | --------- | --------------------------------------------------- |
| **`icon`**       | `ReactNode`        | ✅       | -         | The icon element displayed in the button.           |
| **`label`**      | `ReactNode`        | ✅       | -         | The label element displayed alongside the icon.     |
| **`isExpanded`** | `boolean`          |          | `false`   | If `true`, the button will be in an expanded state. |
| **`iconSide`**   | `'start' \| 'end'` |          | `'start'` | The position of the icon relative to the label.     |

## Examples

In the example, We have provide all use case of FloatButton.

```tsx live
function FloatButtonDemo() {
  const [containerRef, setContainerRef] = React.useState<HTMLDivElement | null>(null)
  const { scrollDirection } = useScrollDirection(containerRef)
  const isExpanded = React.useMemo(
    () => (scrollDirection ? ["up", "none"].includes(scrollDirection) : true),
    [scrollDirection]
  )
  return (
    <div style={{ position: "relative", width: "320px" }}>
      <div
        style={{
          width: "100%",
          height: "500px",
          backgroundColor: "grey",
          overflow: "auto",
        }}
        ref={setContainerRef}
      >
        <div
          style={{ width: "100%", height: "1000px", backgroundColor: "lightcoral" }}
        />
      </div>
      <div style={{ position: "absolute", bottom: "64px", left: "12px" }}>
        <FloatButton
          icon={<Smile />}
          iconSide="end"
          label="Help"
          isExpanded={isExpanded}
        />
      </div>
      <div style={{ position: "absolute", top: "64px", right: "12px" }}>
        <FloatButton
          icon={<Smile />}
          iconSide="start"
          label="Help"
          isExpanded={isExpanded}
        />
      </div>
    </div>
  )
}
```

## CSS

| Class Name                 | Description                                               |
| -------------------------- | --------------------------------------------------------- |
| `.ApolloFloatButton-root`  | Styles applied to the root container of the float button. |
| `.ApolloFloatButton-icon`  | Styles applied to the icon inside the float button.       |
| `.ApolloFloatButton-label` | Styles applied to the label of the float button.          |
