---
title: Icon <PERSON>ton
id: legacy:component:icon-button
slug: /legacy/components/icon-button
---

import { IconButton } from "@design-systems/apollo-ui"
import { Heart } from "@design-systems/apollo-icons"

```tsx
import { IconButton } from "@design-systems/apollo-ui"
import { Heart } from "@design-systems/apollo-icons"
```

The `Icon Button` component is a compact, customizable button primarily used for actions involving icons. It supports various states, sizes, and icon configurations, making it ideal for both primary and secondary actions.

```jsx live
function IconButtonDemo() {
  return (
    <div style={{ display: "flex", gap: "8px", justifyContent: "center", alignItems: "center" }}>
      <IconButton size="small">
        <Heart />
      </IconButton>
      <IconButton size="medium">
        <Heart />
      </IconButton>
      <IconButton
        size="large"
        onClick={() => {
          console.log("Click!")
        }}
      >
        <Heart />
      </IconButton>
      <IconButton disabled size="large">
        <Heart />
      </IconButton>
    </div>
  )
}
```

## Props

> This component extends the type from Apollo's `ButtonProps` which is from [Button](https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/Button/ButtonProps.ts).

| Name           | Type                                             | Required | Default   | Description                                 |
| -------------- | ------------------------------------------------ | -------- | --------- | ------------------------------------------- |
| **`size`**     | `'small' \| 'medium' \| 'large'`                 |          | `'large'` | The size of IconButton                      |
| **`disabled`** | `bool`                                           |          | -         | If `true`, the icon button will be disabled |
| **`onClick`**  | `(event: MouseEvent<HTMLButtonElement>) => void` |          | -         | If `true`, the icon button will be disabled |


## Examples

### Default

This example will show the default usage of component which `size` prop applied.

```jsx live
function IconButtonDemo() {
  return (
   <div style={{ display: "flex", gap: "8px", justifyContent: "center", alignItems: "center" }}>
      <IconButton size="small">
        <Heart />
      </IconButton>
      <IconButton size="medium">
        <Heart />
      </IconButton>
      <IconButton
        size="large"
        onClick={() => {
          console.log("Click!")
        }}
      >
        <Heart />
      </IconButton>
      <IconButton disabled size="large">
        <Heart />
      </IconButton>
    </div>
  )
}
```

## CSS

| Class Name               | Description                          |
| ------------------------ | ------------------------------------ |
| `.ApolloIconButton-root` | Styles applied to the root container |
