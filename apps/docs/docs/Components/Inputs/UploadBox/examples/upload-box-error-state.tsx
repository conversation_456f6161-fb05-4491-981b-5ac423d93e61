import { useState } from "react"
import { UploadBox } from "@apollo/ui"

export default function UploadBoxErrorState() {
  const [file, setFile] = useState<File | null>(null)

  const handleDeleteFile = () => {
    setFile(null)
  }

  return (
    <UploadBox
      label="Upload Document"
      helperText="Upload a document file"
      value={file}
      onDelete={handleDeleteFile}
      onUpload={setFile}
      errorMessage="There was an error uploading your file. Please try again."
    />
  )
}
