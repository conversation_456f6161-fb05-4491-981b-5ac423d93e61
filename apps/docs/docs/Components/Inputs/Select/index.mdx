---
title: Select
id: component:select
slug: /components/select
---

import BasicDemo from "!!raw-loader!./examples/select-basic"
import ControlledDemo from "!!raw-loader!./examples/select-controlled"
import DisabledDemo from "!!raw-loader!./examples/select-disabled"
import ErrorDemo from "!!raw-loader!./examples/select-error"
import FormDemo from "!!raw-loader!./examples/select-form"
import FullWidthDemo from "!!raw-loader!./examples/select-fullwidth"
import LabelHelperTextDemo from "!!raw-loader!./examples/select-label-helpertext"
import RequiredDemo from "!!raw-loader!./examples/select-required"
import DynamicValueDemo from "!!raw-loader!./examples/select-value-types"

import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

The Select component allows users to choose a single value from a list of options. It provides a dropdown interface for selecting from predefined options and supports various states like disabled, error, and required.

<ComponentPreviewUI name="select-preview" code={BasicDemo} />

## Props

| Name               | Type                      | Required | Default | Description                                                         |
| ------------------ | ------------------------- | -------- | ------- | ------------------------------------------------------------------- |
| **`label`**        | `ReactNode`               |          | -       | The label for the select.                                           |
| **`fullWidth`**    | `bool`                    |          | `false` | If `true`, the select will take up the full width of its container. |
| **`required`**     | `bool`                    |          | `false` | If `true`, the select will display a required indicator.            |
| **`helperText`**   | `ReactNode`               |          | -       | The helper text for the select.                                     |
| **`error`**        | `bool`                    |          | `false` | If `true`, the select will display an error state.                  |
| **`disabled`**     | `bool`                    |          | `false` | If `true`, the select will be disabled.                             |
| **`fieldProps`**   | `FieldProps`              |          | -       | Props for the field element.                                        |
| **`placeholder`**  | `string`                  |          | -       | Placeholder text when no value is selected.                         |
| **`value`**        | `string`                  |          | -       | The value of the select, for controlled components.                 |
| **`defaultValue`** | `string`                  |          | -       | The default value of the select, for uncontrolled components.       |
| **`onChange`**     | `(value: string) => void` |          | -       | Callback fired when the value changes.                              |
| **`ref`**          | `Ref<HTMLElement>`        |          | -       | Ref for the select trigger element.                                 |
| **`children`**     | `ReactNode`               | ✓        | -       | The content of the component, typically Select.Option elements.     |

### Select.Option Props

| Name        | Type     | Required | Default | Description                           |
| ----------- | -------- | -------- | ------- | ------------------------------------- |
| **`label`** | `string` | ✓        | -       | The display text of the option.       |
| **`value`** | `any`    | ✓        | -       | The value associated with the option. |

## Examples

### Basic Usage

Basic select with various options.

<ComponentPreviewUI name="select-basic" code={BasicDemo} />

### With Label and Helper Text

Select with a label and helper text.

<ComponentPreviewUI name="select-label-helpertext" code={LabelHelperTextDemo} />

### Error State

Select that displays an error state.

<ComponentPreviewUI name="select-error" code={ErrorDemo} />

### Disabled State

Select that is disabled and not interactive.

<ComponentPreviewUI name="select-disabled" code={DisabledDemo} />

### Required

Select that displays a required indicator.

<ComponentPreviewUI name="select-required" code={RequiredDemo} />

### Full Width

Select that takes up the full width of its container.

<ComponentPreviewUI name="select-fullwidth" code={FullWidthDemo} />

### Controlled Component

Example of a controlled select component.

<ComponentPreviewUI name="select-controlled" code={ControlledDemo} />

### Form Integration

Integrating select with form libraries like react-hook-form.

<ComponentPreviewUI name="select-form" code={FormDemo} />

### Dynamic Value Types

Example of select with dynamic value types.

<ComponentPreviewUI name="dynamic-value-type-form" code={DynamicValueDemo} />

## CSS

| Class Name                       | Description                                        |
| -------------------------------- | -------------------------------------------------- |
| `.ApolloSelect-fieldRoot`        | Styles applied to the field root                   |
| `.ApolloSelect-triggerContainer` | Styles applied to the select trigger container     |
| `.ApolloSelect-trigger`          | Styles applied to the select trigger element       |
| `.ApolloSelect-input`            | Styles applied to the input element                |
| `.ApolloSelect-positioner`       | Styles applied to the dropdown menu positioner     |
| `.ApolloSelect-popup`            | Styles applied to the dropdown popup               |
| `.ApolloSelect-option`           | Styles applied to each option in the dropdown      |
| `.ApolloSelect-menuItem`         | Styles applied to the menu item within each option |
