---
title: Alert
id: component:alert
slug: /components/alert
---

import BasicDemo from "!!raw-loader!./examples/alert-basic"
import CustomDemo from "!!raw-loader!./examples/alert-custom"
import FullWidthDemo from "!!raw-loader!./examples/alert-full-width"

import { ComponentPreviewUI } from "@site/src/components/ComponentPreviewUI"

The Alert component is used to display important messages or feedback to users. It supports various styles for different types of messages such as information, success, warning, and error.

<ComponentPreviewUI name="alert-basic" code={BasicDemo} />

## Props

> **Note**: The props extend the `HTMLAttributes` of the `div` element, omitting the `title` prop.

| Name                 | Type                                          | Required | Default | Description                                     |
| -------------------- | --------------------------------------------- | -------- | ------- | ----------------------------------------------- |
| **`color`**          | `'info' \| 'success' \| 'warning' \| 'error'` |          | `info`  | The color variant of the alert                  |
| **`fullWidth`**      | `boolean`                                     |          | `false` | If `true`, the alert will take up full width    |
| **`title`**          | `ReactNode`                                   |          | -       | The title to display in the alert               |
| **`description`**    | `ReactNode`                                   |          | -       | The description content of the alert            |
| **`startDecorator`** | `ReactNode`                                   |          | -       | Element placed before the content               |
| **`endDecorator`**   | `ReactNode`                                   |          | -       | Element placed after the content                |
| **`onClose`**        | `ReactEventHandler`                           |          | -       | Callback fired when the close button is clicked |

## Examples

### Basic Alerts

Basic alerts with different colors for different message types.

<ComponentPreviewUI name="alert-basic" code={BasicDemo} />

### Custom Elements

Alerts with custom start and end decorators, as well as a closable alert.

<ComponentPreviewUI name="alert-custom" code={CustomDemo} />

### Full Width Alerts

Alerts that span the full width of their container.

<ComponentPreviewUI name="alert-full-width" code={FullWidthDemo} />

## CSS

| Class Name                    | Description                                     |
| ----------------------------- | ----------------------------------------------- |
| `.ApolloAlert-root`           | Styles applied to the root element              |
| `.ApolloAlert-title`          | Styles applied to the title element             |
| `.ApolloAlert-description`    | Styles applied to the description element       |
| `.ApolloAlert-startDecorator` | Styles applied to the start decorator container |
| `.ApolloAlert-endDecorator`   | Styles applied to the end decorator container   |
| `.ApolloAlert-closeButton`    | Styles applied to the close button              |
