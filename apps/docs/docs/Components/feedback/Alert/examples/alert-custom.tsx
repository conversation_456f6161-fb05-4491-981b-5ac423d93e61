import { Alert } from "@apollo/ui"
import { CheckCircle, InfoCircle } from "@design-systems/apollo-icons"

export default function AlertCustom() {
  return (
    <div className="flex flex-col gap-4">
      <Alert
        title="Alert with custom start decorator"
        description="This alert has a custom icon"
        startDecorator={<InfoCircle size={20} color="blue" />}
      />
      <Alert
        title="Alert with end decorator"
        description="This alert has a custom end element"
        endDecorator={<CheckCircle size={16} />}
      />
      <Alert
        title="Closable Alert"
        description="This alert has a close button"
        onClose={() => alert("Alert closed!")}
      />
    </div>
  )
}
