"use client"

import React, { useEffect, useState } from "react"
import { FloatButton } from "@apollo/ui"
import { <PERSON>, Smile } from "@design-systems/apollo-icons"

import { ComponentBox, ComponentGroup } from "./common"

const FloatButtons = () => {
  return (
    <ComponentGroup>
      <ComponentBox direction="horizontal">
        <ComponentBox direction="horizontal">
          <FloatButton icon={<Smile />} isExpanded label="Button" />
          <FloatButton
            iconSide="end"
            isExpanded
            icon={<Smile />}
            label="Button"
          />
          <FloatButton icon={<Smile />} label="Button" />
          <FloatButton icon={<Heart />} label="Button" />
          <FloatButton color="danger" icon={<Heart />} label="Button" />
        </ComponentBox>
      </ComponentBox>
    </ComponentGroup>
  )
}

export default FloatButtons
