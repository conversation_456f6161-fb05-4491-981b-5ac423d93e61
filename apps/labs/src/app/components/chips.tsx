import { Chip } from "@apollo/ui"

import { ComponentBox, ComponentGroup } from "./common"

export function Chips() {
  return (
    <ComponentGroup>
      <ComponentBox>
        <h2 className="text-xl font-semibold">Chip</h2>
        <Chip label="Category: All" />
        <Chip
          label="Category: All"
          onClose={() => {
            console.log("[DEBUG] Close!")
          }}
        />
        <Chip disabled label="Tag" />
        <Chip
          disabled
          onClose={() => {
            console.log("[DEBUG] Close!")
          }}
          label="Tag"
        />
        <Chip size="small" label="Tag" />
        <Chip
          size="small"
          label="Tag"
          onClose={() => {
            console.log("[DEBUG] Close!")
          }}
        />
        <Chip
          label="LongggggggLongggggggLongggggggLonggggggg"
          style={{ width: "100px" }}
        />
        <Chip
          label="Tag"
          onClose={() => {
            console.log("[DEBUG] close event")
          }}
        />
      </ComponentBox>
    </ComponentGroup>
  )
}
