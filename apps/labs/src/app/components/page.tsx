"use client"

import { AccordionDemo } from "./accordion"
import { Alerts } from "./alerts"
import { AutocompletesExample } from "./autocompletes"
import { Buttons } from "./button"
import { CapsuleTabs } from "./capsule-tabs"
import { Checkboxs } from "./checkboxs"
import { Chips } from "./chips"
import { DatePickers } from "./date-pickers"
import FloatButtons from "./float-buttons"
import IconButtons from "./icon-buttons"
import { Inputs } from "./inputs"
import { ModalDemo } from "./modals"
import { Paginations } from "./pagination"
import { ProductCards } from "./product-card"
import { RadioDemo } from "./radio"
import { SelectDemo } from "./select"
import { SortingIconDemo } from "./sorting-icons"
import { Switches } from "./switch"
import { TabsDemo } from "./tabs"
import { Textareas } from "./textareas"
import { Typographys } from "./typographys"
import { UploadBoxDemo } from "./upload-box"

export default function ComponentsPage() {
  return (
    <div className="grid grid-cols-3 gap-2 justify-center items-start p-4">
      <ModalDemo />
      <ProductCards />
      <DatePickers />
      <Paginations />
      <UploadBoxDemo />
      <Alerts />
      <SortingIconDemo />
      <TabsDemo />
      <SelectDemo />
      <AccordionDemo />
      <RadioDemo />
      <AutocompletesExample />
      <div className="flex flex-col gap-2">
        <Chips />
        <Checkboxs />
        <FloatButtons />
        <IconButtons />
        <Switches />
      </div>
      <Typographys />
      <CapsuleTabs />
      <Inputs />
      <Textareas />
      <Buttons />
    </div>
  )
}
