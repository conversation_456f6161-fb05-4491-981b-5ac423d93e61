import type { PropsWithChildren } from "react"
import { Typography } from "@apollo/ui"
import classNames from "classnames"

export const ComponentBox = (
  props: PropsWithChildren<{
    title?: string
    direction?: "vertical" | "horizontal"
    className?: string
  }>
) => (
  <div
    className={`flex flex-col gap-2 justify-start items-start flex-wrap text-black w-full ${props?.className ?? ""}`}
    style={{
      flexDirection: props?.direction === "horizontal" ? "row" : "column",
    }}
  >
    {props?.title && <Typography level="h4">{props.title}</Typography>}
    {props?.children}
  </div>
)

export const ComponentGroup = (
  props: PropsWithChildren<{ className?: string }>
) => (
  <div
    className={classNames(
      "bg-gray-100 p-4 gap-8 flex flex-row flex-wrap rounded-[4px] overflow-hidden text-black",
      props?.className
    )}
  >
    {props?.children}
  </div>
)
