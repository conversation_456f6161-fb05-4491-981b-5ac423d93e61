import { useState } from "react"
import { <PERSON><PERSON>, Select } from "@apollo/ui"
import { Controller, useForm } from "react-hook-form"

import { ComponentBox, ComponentGroup } from "./common"

export function SelectDemo() {
  // State for controlled select
  const [selectedCountry, setSelectedCountry] = useState("ca")

  // React Hook Form setup
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      occupation: "",
    },
  })

  const onSubmit = (data: any) => {
    alert(JSON.stringify(data, null, 2))
  }

  return (
    <ComponentGroup>
      <ComponentBox>
        <Select>
          <Select.Option label="Apple" value="apple" />
          <Select.Option label="Banana" value="banana" />
          <Select.Option label="Cherry" value="cherry" />
          <Select.Option label="Dragon Fruit" value="dragonFruit" />
          <Select.Option label="Elderberry" value="elderberry" />
          <Select.Option label="Fig" value="fig" />
          <Select.Option label="Grape" value="grape" />
          <Select.Option label="Honeydew" value="honeydew" />
          <Select.Option label="Kiwi" value="kiwi" />
          <Select.Option label="Lemon" value="lemon" />
        </Select>
      </ComponentBox>

      <ComponentBox>
        <Select label="Fruits">
          <Select.Option label="Apple" value="apple" />
          <Select.Option label="Banana" value="banana" />
          <Select.Option label="Cherry" value="cherry" />
          <Select.Option label="Dragon Fruit" value="dragonFruit" />
          <Select.Option label="Elderberry" value="elderberry" />
          <Select.Option label="Fig" value="fig" />
          <Select.Option label="Grape" value="grape" />
          <Select.Option label="Honeydew" value="honeydew" />
          <Select.Option label="Kiwi" value="kiwi" />
          <Select.Option label="Lemon" value="lemon" />
        </Select>
      </ComponentBox>

      <ComponentBox>
        <Select
          label="Countries"
          helperText="Select your country"
          error
          required
        >
          <Select.Option label="Australia" value="au" />
          <Select.Option label="Brazil" value="br" />
          <Select.Option label="Canada" value="ca" />
          <Select.Option label="Denmark" value="dk" />
          <Select.Option label="Egypt" value="eg" />
          <Select.Option label="France" value="fr" />
          <Select.Option label="Germany" value="de" />
          <Select.Option label="Hungary" value="hu" />
          <Select.Option label="India" value="in" />
          <Select.Option label="Japan" value="jp" />
        </Select>
      </ComponentBox>

      <ComponentBox>
        <Select
          disabled
          label="Languages"
          helperText="Select your preferred language"
          required
        >
          <Select.Option label="English" value="en" />
          <Select.Option label="Spanish" value="es" />
          <Select.Option label="French" value="fr" />
          <Select.Option label="German" value="de" />
          <Select.Option label="Italian" value="it" />
          <Select.Option label="Portuguese" value="pt" />
          <Select.Option label="Russian" value="ru" />
          <Select.Option label="Japanese" value="ja" />
          <Select.Option label="Chinese" value="zh" />
          <Select.Option label="Arabic" value="ar" />
        </Select>
      </ComponentBox>

      {/* Uncontrolled Select with defaultValue */}
      <ComponentBox>
        <Select
          label="Vehicles"
          defaultValue="car"
          helperText="Select your preferred vehicle (uncontrolled)"
          name="vehicle-select"
        >
          <Select.Option label="Car" value="car" />
          <Select.Option label="Motorcycle" value="motorcycle" />
          <Select.Option label="Bicycle" value="bicycle" />
          <Select.Option label="Bus" value="bus" />
          <Select.Option label="Train" value="train" />
          <Select.Option label="Subway" value="subway" />
          <Select.Option label="Airplane" value="airplane" />
          <Select.Option label="Boat" value="boat" />
          <Select.Option label="Helicopter" value="helicopter" />
          <Select.Option label="Scooter" value="scooter" />
        </Select>
      </ComponentBox>

      {/* Controlled Select */}
      <ComponentBox>
        <Select
          fullWidth
          label="Countries"
          value={selectedCountry}
          onChange={setSelectedCountry}
          helperText={`Current selection: ${selectedCountry} (controlled)`}
        >
          <Select.Option label="Australia" value="au" />
          <Select.Option label="Brazil" value="br" />
          <Select.Option label="Canada" value="ca" />
          <Select.Option label="Denmark" value="dk" />
          <Select.Option label="Egypt" value="eg" />
          <Select.Option label="France" value="fr" />
          <Select.Option label="Germany" value="de" />
          <Select.Option label="Hungary" value="hu" />
          <Select.Option label="India" value="in" />
          <Select.Option label="Japan" value="jp" />
        </Select>

        <div style={{ marginTop: "10px" }}>
          <button onClick={() => setSelectedCountry("br")}>
            Set to Brazil
          </button>
          <button
            onClick={() => setSelectedCountry("jp")}
            style={{ marginLeft: "10px" }}
          >
            Set to Japan
          </button>
        </div>
      </ComponentBox>

      <ComponentBox>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Controller
            name="occupation"
            control={control}
            rules={{ required: "Please select an occupation" }}
            render={({ field }) => (
              <Select
                label="Occupation"
                helperText={
                  errors.occupation
                    ? errors.occupation.message
                    : "Select your occupation"
                }
                error={!!errors.occupation}
                required
                value={field.value}
                onChange={field.onChange}
              >
                <Select.Option label="Engineer" value="engineer" />
                <Select.Option label="Designer" value="designer" />
                <Select.Option
                  label="Product Manager"
                  value="product_manager"
                />
                <Select.Option label="Developer" value="developer" />
                <Select.Option label="Marketer" value="marketer" />
                <Select.Option label="Data Scientist" value="data_scientist" />
                <Select.Option label="Consultant" value="consultant" />
                <Select.Option label="Sales Representative" value="sales_rep" />
                <Select.Option
                  label="Customer Support"
                  value="customer_support"
                />
                <Select.Option label="Other" value="other" />
              </Select>
            )}
          />
          <Button>Submit</Button>
        </form>
      </ComponentBox>
    </ComponentGroup>
  )
}
