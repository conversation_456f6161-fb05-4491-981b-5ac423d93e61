import { Button, Textarea } from "@apollo/ui"
import { InfoCircle } from "@design-systems/apollo-icons"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"

import { ComponentBox, ComponentGroup } from "./common"

export const Textareas = () => {
  const { register, handleSubmit, formState } = useForm({
    defaultValues: {
      name: "",
    },
    resolver: zodResolver(
      z
        .object({
          name: z
            .string({
              required_error: "name is required na",
            })
            .min(1, "name is required na"),
        })
        .required({
          name: true,
        })
    ),
  })

  const onSubmit = ({ name }: any) => {
    console.log("[DEBUG Form] value", name)
  }

  return (
    <>
      <ComponentGroup>
        <ComponentBox>
          <Textarea
            required
            error
            minRows={2}
            label="Label"
            helperText={"lorem ipsum"}
            placeholder="Placeholder"
          />
          <Textarea
            required
            disabled
            minRows={2}
            error={!!formState?.errors?.name}
            label="Label"
            helperText={"lorem ipsum"}
            placeholder="Placeholder"
          />
          <Textarea
            required
            minRows={2}
            error={!!formState?.errors?.name}
            label="Label"
            helperText={"lorem ipsum"}
            placeholder="Placeholder"
          />
        </ComponentBox>
        <ComponentBox>
          <Textarea
            size="small"
            required
            error
            minRows={2}
            label="Label"
            helperText={"lorem ipsum"}
            placeholder="Placeholder"
          />
          <Textarea
            size="small"
            required
            disabled
            minRows={2}
            error={!!formState?.errors?.name}
            label="Label"
            helperText={"lorem ipsum"}
            placeholder="Placeholder"
          />
          <Textarea
            size="small"
            required
            minRows={2}
            error={!!formState?.errors?.name}
            label="Label"
            helperText={"lorem ipsum"}
            placeholder="Placeholder"
          />
          <Textarea
            fullWidth
            size="small"
            required
            minRows={2}
            error={!!formState?.errors?.name}
            label="Label"
            helperText={"lorem ipsum"}
            placeholder="Placeholder"
          />
        </ComponentBox>
        <ComponentBox>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Textarea
              {...register("name")}
              fullWidth
              size="small"
              required
              minRows={2}
              label="Label"
              error={!!formState?.errors?.name}
              helperText={formState?.errors?.name?.message ?? "lorem ipsum"}
              placeholder="Placeholder"
            />
            <Button type="submit">Submit</Button>
          </form>
        </ComponentBox>
      </ComponentGroup>
    </>
  )
}
