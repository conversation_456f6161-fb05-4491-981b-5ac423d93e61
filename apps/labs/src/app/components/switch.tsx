import { useState } from "react"
import { <PERSON><PERSON>, Switch } from "@apollo/ui"
import { Controller, useForm } from "react-hook-form"

import { ComponentBox, ComponentGroup } from "./common"

export const Switches = () => {
  const [isChecked, setIsChecked] = useState(false)
  const { control, handleSubmit, register } = useForm({
    defaultValues: {
      switchField: false,
    },
  })

  const onSubmit = (data: any) => {
    console.log("Form Data:", data)
  }

  return (
    <ComponentGroup>
      <ComponentBox>
        <Switch
          defaultChecked
          onCheckedChange={(checked) => console.log("Checked:", checked)}
        />
        <Switch
          onCheckedChange={(checked) => console.log("Unchecked:", checked)}
        />
        <Switch
          label="Lorem Ipsum"
          actionText="On/Off"
          onCheckedChange={(checked) => console.log("Unchecked:", checked)}
        />
        <Switch disabled defaultChecked />
        <Switch disabled />
        <Switch
          checked={isChecked}
          onCheckedChange={(checked) => {
            setIsChecked(checked)
            console.log("Controlled Switch Checked:", checked)
          }}
        />
        <br />
        <form onSubmit={handleSubmit(onSubmit)}>
          <Controller
            name="switchField"
            control={control}
            render={({ field: { value, ...field } }) => (
              <Switch
                {...field}
                checked={value}
                onCheckedChange={(checked) => {
                  console.log("checked", checked)
                  field.onChange(checked)
                  console.log("React Hook Form Switch Checked:", checked)
                }}
              />
            )}
          />
          <Button size="small" type="submit">
            Submit
          </Button>
        </form>
      </ComponentBox>
    </ComponentGroup>
  )
}
