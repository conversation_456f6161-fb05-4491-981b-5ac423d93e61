import { Button, Input } from "@apollo/ui"
import { InfoCircle } from "@design-systems/apollo-icons"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"

import { ComponentBox, ComponentGroup } from "./common"

export const Inputs = () => {
  const { register, handleSubmit, formState } = useForm({
    defaultValues: {
      name: "",
    },
    resolver: zodResolver(
      z
        .object({
          name: z
            .string({
              required_error: "name is required na",
            })
            .min(1, "name is required na"),
        })
        .required({
          name: true,
        })
    ),
  })

  const onSubmit = ({ name }: any) => {
    console.log("[DEBUG Form] value", name)
  }

  return (
    <>
      <ComponentGroup>
        <ComponentBox direction="horizontal">
          <Input value="Type here" />
          <Input fullWidth value="Type here" />
          <Input error value="Type here" />
          <Input disabled value="Type here" />
          <Input placeholder="Placeholder" />
          <Input error placeholder="Placeholder" />
          <Input disabled placeholder="Placeholder" />
          <Input
            size="small"
            label="lorem"
            helperText="test message"
            value="Type here"
          />
          <Input size="small" error value="Type here" />
          <Input size="small" disabled value="Type here" />
          <Input size="small" placeholder="Placeholder" />
          <Input size="small" error placeholder="Placeholder" />
          <Input size="small" disabled placeholder="Placeholder" />
          <Input
            endDecorator={<InfoCircle size={16} />}
            placeholder="Placeholder"
          />
          <Input
            endDecorator={<InfoCircle size={16} />}
            error
            placeholder="Placeholder"
          />
          <Input
            endDecorator={<InfoCircle size={16} />}
            disabled
            placeholder="Placeholder"
          />
          <Input
            startDecorator={<InfoCircle size={16} />}
            placeholder="Placeholder"
          />
          <Input
            startDecorator={<InfoCircle size={16} />}
            error
            placeholder="Placeholder"
          />
          <Input
            startDecorator={<InfoCircle size={16} />}
            disabled
            placeholder="Placeholder"
          />
          <Input
            label="Name"
            helperText="lorem ipsum"
            placeholder="Placeholder"
          />
          <Input
            label="Name"
            required
            helperText="lorem ipsum"
            placeholder="Placeholder"
          />
          <Input
            label="Name"
            helperText="lorem ipsum"
            error
            startDecorator={<InfoCircle size={16} />}
            placeholder="Placeholder"
          />
        </ComponentBox>
        <ComponentBox>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Input
              {...register("name", { required: true })}
              label="Test Form"
              required
              error={!!formState?.errors?.name}
              helperText={formState?.errors?.name?.message ?? "lorem ipsum"}
              placeholder="Placeholder"
            />
            <Button type="submit">Submit</Button>
          </form>
        </ComponentBox>
      </ComponentGroup>
    </>
  )
}
