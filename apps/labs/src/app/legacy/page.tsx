"use client"

import { useState } from "react"
import {
  Autocomplete,
  Button,
  Checkbox,
  // createTheme,
  DateInput,
  EmptyState,
  Input,
  Modal,
  ThemeProvider,
} from "@design-systems/apollo-ui"

import { ComponentGroup } from "../components/common"

const options = Array.from({ length: 10 }, (_, i) => ({
  label: `Option ${i + 1}`,
  value: `value${i + 1}`,
}))

export default function LegacyPage() {
  const [isOpen, setIsOpen] = useState(false)
  const [date, setDate] = useState<Date | null>(new Date())
  const [isOpenSmall, setIsOpenSmall] = useState(false)
  return (
    <ThemeProvider
      scope="legacy"
      // theme={
      //   // createTheme({
      //   //   colors: {
      //   //     "apl-colors-surface-action-primary-default": "#CAA3D3",
      //   //     "apl-colors-border-primary-default": "#CAA3D3",
      //   //     "apl-colors-border-primary-subdued": "#D5B5DC",
      //   //     "apl-colors-content-primary-default": "#8C6C94",
      //   //   } as any,
      //   // }) as any
      // }
    >
      <ComponentGroup className="bg-gray-400">
        <EmptyState
          title="ยังไม่มีข้อมูล"
          description="กรุณากดปุ่มสร้างเพื่อเริ่มทำรายการกรุณากดปุ่มสร้างเพื่อเริ่มทำรายการกรุณากดปุ่มสร้างเพื่อเริ่มทำรายการกรุณากดปุ่มสร้างเพื่อเริ่มทำรายการ"
          name="FileNotFound"
        />
        <EmptyState
          title="Add More Information"
          description="Lorem ipsu,m"
          name="AddInformation"
          actions={
            <>
              <Button fullWidth variant="solid">
                Click Me
              </Button>
              <Button fullWidth variant="outline">
                Click Me
              </Button>
            </>
          }
        />
        <Button variant="solid" onClick={() => setIsOpen(true)}>
          Open Modal with Icon
        </Button>
        <Modal
          scrollableContent
          onOk={() => {
            console.log("[Click Event]: I'm okay")
            setIsOpen(false)
          }}
          onCancel={() => {
            console.log("[Click Event]: Canceled")
            setIsOpen(false)
          }}
          open={isOpen}
          onClose={() => {
            setIsOpen(false)
          }}
          size="full"
          maxWidth="1000px"
          header="Modal with Icon"
        >
          <div className="w-[100px] bg-amber-300 h-[2000px]">Test</div>
          <div className="text-black h-fit">
            <Autocomplete
              multiple
              label="Label"
              limitTags={2}
              placeholder="search"
              value={options}
              options={options}
            />
            This modal includes an icon. Once upon a time, there was a forest
            where plenty of birds lived and built their nests on the trees.
          </div>
        </Modal>
        <Button fullWidth>Test 2</Button>
        <div className="flex flex-col w-[600px] gap-2">
          <Autocomplete
            multiple
            label="Label"
            limitTags={2}
            placeholder="search"
            value={options}
            disablePortal={false}
            options={options}
          />
          <Autocomplete
            multiple
            label="Label"
            inputProps={{
              size: "small",
            }}
            value={options}
            limitTags={3}
            placeholder="search"
            hideOverflowTag
            options={options}
          />
        </div>
        <Input error label="Label" helperText="helperText" />
        <Input
          error
          label="Label"
          helperText={
            <div>
              Tetsasdas<span style={{ color: "red" }}>TTT</span>
            </div>
          }
        />
        <Checkbox label="test" />
        <Checkbox indeterminate label="test" />
        <Checkbox disabled label="test" />
        <Checkbox checked disabled label="test" />
        <Checkbox indeterminate disabled label="test" />
        <DateInput
          value={date}
          format="dd/MM/yyyy HH:mm:ss"
          showTimeSelect
          portal
          onChange={(date) => {
            console.log("date", date)
            setDate(date)
          }}
          helperText="helperText"
          label="Label"
        />
        <DateInput
          value={date}
          minDate={new Date("2025-06-26")}
          // format="dd/MM/yyyy"
          onChange={(date) => {
            console.log("date", date)
            setDate(date)
          }}
          helperText="helperText"
          label="Label"
        />
        <>
          <Button variant="solid" onClick={() => setIsOpenSmall(true)}>
            Open Negative Modal
          </Button>
          <Modal
            // onOk={() => {
            //   console.log("[Click Event]: I'm okay")
            // }}
            open={isOpenSmall}
            onClose={() => {
              setIsOpenSmall(false)
            }}
            header="ต้องการส่งคำขอโปรโมชั่นนี้"
          >
            <div>This modal is specifically for negative confirmation.</div>
          </Modal>
        </>
      </ComponentGroup>
    </ThemeProvider>
  )
}
