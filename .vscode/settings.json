{"editor.defaultFormatter": "esbenp.prettier-vscode", "javascript.validate.enable": false, "eslint.workingDirectories": [{"mode": "auto"}], "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "jest.runMode": "on-demand", "[css]": {"editor.defaultFormatter": "vscode.css-language-features"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}