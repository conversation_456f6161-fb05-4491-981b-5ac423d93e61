#!/usr/bin/env node

/**
 * Script to convert Figma design tokens to W3C Design Tokens format
 *
 * This script reads Figma token files from the figma/ directory and converts them
 * to the W3C Design Tokens Community Group specification format.
 *
 * Usage: node scripts/figma-to-w3c-tokens.js [input-dir] [output-file]
 *
 * @see https://www.designtokens.org/tr/drafts/format/
 */

const fs = require("fs")
const path = require("path")

// Default paths
const DEFAULT_INPUT_DIR = path.join(__dirname, "../figma")
const DEFAULT_OUTPUT_FILE = path.join(__dirname, "../build/w3c-tokens.json")

/**
 * Convert Figma color value to W3C color format
 * @param {string} hexValue - Hex color value from Figma
 * @returns {Object} W3C color object
 */
function convertColor(hexValue) {
  // Remove # if present
  const hex = hexValue.replace("#", "")

  // Handle alpha channel if present (8-digit hex)
  let alpha = 1
  let colorHex = hex

  if (hex.length === 8) {
    alpha = parseInt(hex.slice(6, 8), 16) / 255
    colorHex = hex.slice(0, 6)
  }

  // Convert hex to RGB components (0-1 range)
  const r = parseInt(colorHex.slice(0, 2), 16) / 255
  const g = parseInt(colorHex.slice(2, 4), 16) / 255
  const b = parseInt(colorHex.slice(4, 6), 16) / 255

  const colorValue = {
    colorSpace: "srgb",
    components: [r, g, b],
    hex: `#${colorHex.toUpperCase()}`,
  }

  if (alpha < 1) {
    colorValue.alpha = alpha
  }

  return colorValue
}

/**
 * Convert Figma dimension value to W3C dimension format
 * @param {number} value - Numeric value from Figma
 * @param {string} unit - Unit type (defaults to 'px')
 * @returns {Object} W3C dimension object
 */
function convertDimension(value, unit = "px") {
  return {
    value: value,
    unit: unit,
  }
}

/**
 * Convert Figma font weight to W3C font weight format
 * @param {string} weight - Font weight string from Figma
 * @returns {string|number} W3C font weight value
 */
function convertFontWeight(weight) {
  const weightMap = {
    Thin: 100,
    "Thin It": 100,
    "Extra Light": 200,
    Light: 300,
    Regular: 400,
    Medium: 500,
    "Medium It": 500,
    "Semi Bold": 600,
    Bold: 700,
    "Bold It": 700,
    Black: 900,
    "Black It": 900,
  }

  return weightMap[weight] || weight
}

/**
 * Determine W3C token type from Figma token type and name
 * @param {string} figmaType - Figma token type
 * @param {string} tokenName - Token name for context
 * @returns {string} W3C token type
 */
function getW3CType(figmaType, tokenName = "") {
  // Handle special cases based on token name
  if (tokenName.includes("font-weight") || tokenName.includes("fontWeight")) {
    return "fontWeight"
  }
  if (tokenName.includes("font-family") || tokenName.includes("fontFamily")) {
    return "fontFamily"
  }
  if (tokenName.includes("font-size") || tokenName.includes("fontSize")) {
    return "dimension"
  }
  if (tokenName.includes("line-height") || tokenName.includes("lineHeight")) {
    return "number"
  }

  const typeMap = {
    color: "color",
    number: "dimension",
    string: "fontFamily",
  }

  return typeMap[figmaType] || figmaType
}

/**
 * Convert Figma token name to Style Dictionary compatible name
 * @param {string} name - Figma token name
 * @returns {string} Sanitized token name
 */
function sanitizeTokenName(name) {
  return name
    .toLowerCase() // Convert to lowercase
    .replace(/[{}.]/g, "-") // Remove W3C invalid characters
    .replace(/[\s/]/g, "-") // Replace spaces and slashes with hyphens
    .replace(/[^\w-]/g, "-") // Replace any other special characters with hyphens
    .replace(/-+/g, "-") // Replace multiple consecutive hyphens with single hyphen
    .replace(/^-+|-+$/g, "") // Remove leading/trailing hyphens
}

/**
 * Clean collection name by removing "base" and "alias" prefixes
 * @param {string} name - Collection name
 * @returns {string} Cleaned collection name
 */
function cleanCollectionName(name) {
  return sanitizeTokenName(name)
    .replace(/^(base-|alias-)/, "") // Remove base- and alias- prefixes
    .replace(/^elevations?$/, "elevation") // Normalize both "elevation" and "elevations" to "elevation"
}

/**
 * Get token type from collection name
 * @param {string} collectionName - Original collection name
 * @returns {string} Token type
 */
function getTokenTypeFromCollection(collectionName) {
  const name = collectionName.toLowerCase().trim()

  if (name.includes("color")) return "color"
  if (name.includes("spacing")) return "spacing"
  if (name.includes("typography")) return "typography"
  if (name.includes("radius")) return "radius"
  if (name.includes("elevation")) return "elevation"

  // Default fallback
  return cleanCollectionName(collectionName)
}

/**
 * Create reference path from Figma token name
 * @param {string} figmaName - Original Figma token name
 * @returns {string} Dot-separated path for references
 */
function createReferencePath(figmaName) {
  if (figmaName.includes("/")) {
    // For tokens with "/", create nested path
    return figmaName
      .split("/")
      .map((part) => sanitizeTokenName(part))
      .join(".")
  } else {
    // For tokens without "/", use sanitized name directly
    return sanitizeTokenName(figmaName)
  }
}

/**
 * Create nested object structure from token name based on original Figma name
 * @param {string} originalName - Original Figma token name
 * @param {string} tokenName - Sanitized token name
 * @param {Object} tokenValue - Token object with $type and $value
 * @param {Object} targetObject - Object to add the nested structure to
 */
function createNestedStructure(
  originalName,
  tokenName,
  tokenValue,
  targetObject
) {
  // If original name contains "/", use that for nesting structure
  if (originalName.includes("/")) {
    const parts = originalName.split("/").map((part) => sanitizeTokenName(part))
    let current = targetObject

    // Navigate/create nested structure based on "/" separators
    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i]
      if (!current[part]) {
        current[part] = {}
      }
      current = current[part]
    }

    // Add the final token
    const finalKey = parts[parts.length - 1]
    current[finalKey] = tokenValue
  } else {
    // For tokens without "/", keep the original structure (like green-pine)
    targetObject[tokenName] = tokenValue
  }
}

/**
 * Deep merge two objects, preserving nested structure
 * @param {Object} target - Target object
 * @param {Object} source - Source object
 * @returns {Object} Merged object
 */
function deepMerge(target, source) {
  const result = { ...target }

  for (const [key, value] of Object.entries(source)) {
    if (key.startsWith("$")) {
      // Preserve metadata properties
      result[key] = value
    } else if (value && typeof value === "object" && !value.$value) {
      // Recursively merge nested objects (but not tokens with $value)
      result[key] = deepMerge(result[key] || {}, value)
    } else {
      // Direct assignment for tokens and primitive values
      result[key] = value
    }
  }

  return result
}

/**
 * Process a single Figma variable and convert to W3C format
 * @param {Object} variable - Figma variable object
 * @param {string} collectionName - Name of the collection
 * @returns {Object} W3C token object
 */
function processVariable(variable, collectionName) {
  const tokenName = sanitizeTokenName(variable.name)
  const w3cType = getW3CType(variable.type, variable.name)

  let tokenValue

  // Handle different token types
  switch (variable.type) {
    case "color":
      tokenValue = convertColor(variable.value)
      break

    case "number":
      tokenValue = convertDimension(variable.value)
      break

    case "string":
      // Handle font weights specially
      if (
        variable.name.includes("font-weight") ||
        variable.name.includes("fontWeight")
      ) {
        tokenValue = convertFontWeight(variable.value)
      } else {
        tokenValue = variable.value
      }
      break

    default:
      tokenValue = variable.value
  }

  const token = {
    $type: w3cType,
    $value: tokenValue,
  }

  // Add description if available
  if (variable.description) {
    token.$description = variable.description
  }

  return { name: tokenName, token }
}

/**
 * Process alias/reference tokens
 * @param {Object} variable - Figma variable with alias
 * @param {string} collectionName - Name of the collection
 * @returns {Object} W3C token object with reference
 */
function processAlias(
  variable,
  collectionName,
  tokenType,
  sourceType = "legacy"
) {
  const tokenName = sanitizeTokenName(variable.name)
  const w3cType = getW3CType(variable.type, variable.name)

  // Convert Figma alias to W3C reference format
  // Handle cases where value might be undefined or name might be missing
  if (!variable.value || !variable.value.name) {
    console.warn(
      `⚠️  Invalid alias token: ${variable.name} - missing reference data`
    )
    return { name: tokenName, token: { $type: w3cType, $value: null } }
  }

  let reference
  if (variable.value.collection) {
    // Full reference with collection
    const referencedCollectionOriginal = variable.value.collection
      .trim()
      .toLowerCase()
    const isReferencedAlias = referencedCollectionOriginal.includes("alias")

    const referencedPath = createReferencePath(variable.value.name)

    // Determine the correct prefix based on the referenced collection and source type
    let prefix
    if (isReferencedAlias) {
      prefix = "alias"
    } else if (
      // referencedCollectionOriginal.includes('base color') ||
      referencedCollectionOriginal.includes("base ")
    ) {
      // Base collections should be referenced as 'base' (system tokens)
      prefix = "base"
    } else {
      // Other collections (like global)
      prefix = "global"
    }

    // For references with collection, use the token type from the referenced collection
    const referencedTokenType = getTokenTypeFromCollection(
      variable.value.collection.trim()
    )
    reference = `{${prefix}.${referencedTokenType}.${referencedPath}}`
  } else {
    // Reference without collection (assume same collection)
    const referencedPath = createReferencePath(variable.value.name)
    const currentCollectionOriginal = collectionName.toLowerCase()
    const isCurrentAlias = currentCollectionOriginal.includes("alias")

    // Determine prefix based on current context and referenced token
    let prefix
    if (isCurrentAlias) {
      // Alias tokens should reference base tokens
      prefix = "base"
    } else {
      // Base tokens should reference global tokens (for elevation, spacing, etc.)
      prefix = "global"
    }

    reference = `{${prefix}.${tokenType}.${referencedPath}}`
  }

  const token = {
    $type: w3cType,
    $value: reference,
  }

  if (variable.description) {
    token.$description = variable.description
  }

  return { name: tokenName, token }
}

/**
 * Convert Figma tokens file to Style Dictionary compatible W3C format
 * @param {Object} figmaData - Parsed Figma tokens JSON
 * @returns {Object} W3C tokens object with apl prefix
 */
function convertFigmaToW3C(figmaData, sourceType, fileName = null) {
  const organizedTokens = {
    global: {},
    base: {},
    alias: {},
  }

  // First pass: Count total unique modes for each token type
  const modeCountByType = {
    global: {},
    base: {},
    alias: {},
  }

  figmaData.collections.forEach((collection) => {
    const originalName = collection.name.toLowerCase()
    const isAlias = originalName.includes("alias")

    let tokenType
    tokenType = cleanCollectionName(collection.name)

    // Determine type key based on source type
    let typeKey
    if (sourceType === "global") {
      typeKey = "global"
    } else {
      typeKey = isAlias ? "alias" : "base"
    }

    if (!modeCountByType[typeKey][tokenType]) {
      modeCountByType[typeKey][tokenType] = new Set()
    }

    collection.modes.forEach((mode) => {
      let modeName = sanitizeTokenName(mode.name)

      // Skip "Style" mode entirely for ALL token types - don't generate files for it
      if (mode.name === "Style") {
        return
      }

      modeCountByType[typeKey][tokenType].add(modeName)
    })
  })

  // Process each collection
  figmaData.collections.forEach((collection) => {
    const originalName = collection.name.toLowerCase()
    const isAlias = originalName.includes("alias")

    // Check if all modes in this collection are "Style" modes - if so, skip entirely
    const allModesAreStyle = collection.modes.every(
      (mode) => mode.name === "Style"
    )
    if (allModesAreStyle) {
      return // Skip this entire collection
    }

    // Determine target structure based on source type
    let targetRoot, typeKey

    if (sourceType === "global") {
      // Global tokens always go to global section
      targetRoot = organizedTokens.global
      typeKey = "global"
    } else {
      // Legacy: use normal base/alias logic
      targetRoot = isAlias ? organizedTokens.alias : organizedTokens.base
      typeKey = isAlias ? "alias" : "base"
    }

    // Special handling for typography collections
    let tokenType
    let actualTokenType // The actual type for file generation

    tokenType = cleanCollectionName(collection.name)
    actualTokenType = tokenType

    // Create nested structure by type (use actualTokenType for file generation)
    if (!targetRoot[actualTokenType]) {
      targetRoot[actualTokenType] = {}
    }

    // Check if we need mode separation based on total unique modes for this type
    let hasMultipleModes = modeCountByType[typeKey][tokenType].size > 1

    // Special case: Typography collection (with "Style" mode) should create subfolder
    // if (tokenType === "typography-style") {
    //   hasMultipleModes = true // Force mode separation to create subfolder
    // }

    // Process each mode
    collection.modes.forEach((mode) => {
      let modeName = sanitizeTokenName(mode.name)

      // Skip "Style" mode entirely for ALL token types - don't generate files for it
      if (mode.name === "Style") {
        return
      }

      // Determine target container
      let targetContainer
      if (hasMultipleModes) {
        // Create mode structure when multiple modes exist
        if (!targetRoot[actualTokenType][modeName]) {
          targetRoot[actualTokenType][modeName] = {
            $description: `${isAlias ? "Alias" : "Base"} tokens from ${collection.name} collection - ${mode.name}`,
          }
        }
        targetContainer = targetRoot[actualTokenType][modeName]
      } else {
        // Use type directly when only one mode exists
        if (!targetRoot[actualTokenType].$description) {
          targetRoot[actualTokenType].$description =
            `${isAlias ? "Alias" : "Base"} tokens from ${collection.name} collection`
        }
        targetContainer = targetRoot[actualTokenType]
      }

      // Process each variable in the mode
      mode.variables.forEach((variable) => {
        let processedToken

        if (
          variable.isAlias &&
          variable.value &&
          typeof variable.value === "object"
        ) {
          // Handle alias/reference tokens
          processedToken = processAlias(
            variable,
            collection.name,
            actualTokenType,
            sourceType
          )
        } else {
          // Handle regular tokens
          processedToken = processVariable(variable, collection.name)
        }

        // Remove collection name prefix from variable name if it exists
        let cleanVariableName = variable.name
        const collectionPrefix = collection.name + "/"
        if (cleanVariableName.startsWith(collectionPrefix)) {
          cleanVariableName = cleanVariableName.substring(
            collectionPrefix.length
          )
        }

        // For alias color tokens, remove "Schemes" and "Extended" prefixes
        if (isAlias && actualTokenType === "color") {
          cleanVariableName = cleanVariableName
            .replace(/^Schemes\//, "")
            .replace(/^Extended\//, "")
        }

        // Create nested structure for token using cleaned variable name
        createNestedStructure(
          cleanVariableName,
          processedToken.name,
          processedToken.token,
          targetContainer
        )
      })
    })
  })

  return organizedTokens
}

/**
 * Merge tokens from one organized structure into another
 * @param {Object} target - Target token structure
 * @param {Object} source - Source token structure to merge
 */
function mergeOrganizedTokens(target, source) {
  // Merge each section (global, base, alias)
  ;["global", "base", "alias"].forEach((section) => {
    if (source[section]) {
      for (const [tokenType, typeData] of Object.entries(source[section])) {
        if (!target[section][tokenType]) {
          target[section][tokenType] = typeData
        } else {
          // Check if typeData has mode structure or is direct tokens
          const hasModesStructure = Object.keys(typeData).some(
            (key) =>
              !key.startsWith("$") &&
              typeof typeData[key] === "object" &&
              typeData[key].$description
          )

          if (hasModesStructure) {
            // Merge mode-based structure
            for (const [modeName, tokens] of Object.entries(typeData)) {
              if (!modeName.startsWith("$")) {
                if (!target[section][tokenType][modeName]) {
                  target[section][tokenType][modeName] = tokens
                } else {
                  target[section][tokenType][modeName] = deepMerge(
                    target[section][tokenType][modeName],
                    tokens
                  )
                }
              }
            }
          } else {
            // Merge direct token structure
            target[section][tokenType] = deepMerge(
              target[section][tokenType],
              typeData
            )
          }
        }
      }
    }
  })
}

/**
 * Generate separate token files by type and mode
 * @param {Object} organizedTokens - Organized tokens by global/base/alias
 * @param {string} outputDir - Output directory for token files
 */
function generateSeparateFiles(organizedTokens, outputDir) {
  // Create output directory structure
  const globalDir = path.join(outputDir, "global")
  const systemDir = path.join(outputDir, "system")
  const baseDir = path.join(systemDir, "base")
  const aliasDir = path.join(systemDir, "alias")

  if (!fs.existsSync(globalDir)) {
    fs.mkdirSync(globalDir, { recursive: true })
  }
  if (!fs.existsSync(baseDir)) {
    fs.mkdirSync(baseDir, { recursive: true })
  }
  if (!fs.existsSync(aliasDir)) {
    fs.mkdirSync(aliasDir, { recursive: true })
  }

  // Generate global token files by type and mode
  Object.entries(organizedTokens.global).forEach(([tokenType, typeData]) => {
    // Check if this type has modes or is a direct token structure
    const hasModesStructure = Object.keys(typeData).some(
      (key) =>
        !key.startsWith("$") &&
        typeof typeData[key] === "object" &&
        typeData[key].$description
    )

    if (hasModesStructure) {
      // Generate separate files for each mode
      Object.entries(typeData).forEach(([modeName, tokens]) => {
        if (!modeName.startsWith("$")) {
          const fileName = `${tokenType}:${modeName}.json`
          const filePath = path.join(globalDir, fileName)

          // Extract tokens without mode wrapper
          const { $description, ...tokensWithoutMode } = tokens
          const fileContent = {
            global: {
              [tokenType]: {
                $description,
                ...tokensWithoutMode,
              },
            },
          }
          fs.writeFileSync(filePath, JSON.stringify(fileContent, null, 2))
          console.log(`   ✅ Generated global/${fileName}`)
        }
      })
    } else {
      // Generate single file for this type
      const fileName = `${tokenType}.json`
      const filePath = path.join(globalDir, fileName)
      const fileContent = {
        global: {
          [tokenType]: typeData,
        },
      }
      fs.writeFileSync(filePath, JSON.stringify(fileContent, null, 2))
      console.log(`   ✅ Generated global/${fileName}`)
    }
  })

  // Generate base token files by type and mode
  Object.entries(organizedTokens.base).forEach(([tokenType, typeData]) => {
    // Check if this type has modes or is a direct token structure
    const hasModesStructure = Object.keys(typeData).some(
      (key) =>
        !key.startsWith("$") &&
        typeof typeData[key] === "object" &&
        typeData[key].$description
    )

    if (hasModesStructure) {
      // Generate separate files for each mode
      Object.entries(typeData).forEach(([modeName, tokens]) => {
        if (!modeName.startsWith("$")) {
          // Special handling for typography style mode - create subfolder
          let fileName, filePath
          if (tokenType === "typography" && modeName === "style") {
            const subDir = path.join(baseDir, tokenType)
            fs.mkdirSync(subDir, { recursive: true })
            fileName = `${modeName}.json`
            filePath = path.join(subDir, fileName)
          } else {
            fileName = `${tokenType}:${modeName}.json`
            filePath = path.join(baseDir, fileName)
          }

          // Extract tokens without mode wrapper
          const { $description, ...tokensWithoutMode } = tokens
          const fileContent = {
            base: {
              [tokenType]: {
                $description,
                ...tokensWithoutMode,
              },
            },
          }
          fs.writeFileSync(filePath, JSON.stringify(fileContent, null, 2))

          // Log with appropriate path
          // if (tokenType === 'typography' && modeName === 'style') {
          //   console.log(`   ✅ Generated system/base/${tokenType}/${fileName}`);
          // } else {
          console.log(`   ✅ Generated system/base/${fileName}`)
          // }
        }
      })
    } else {
      // Generate single file for type (no modes)
      const fileName = `${tokenType}.json`
      const filePath = path.join(baseDir, fileName)
      const fileContent = {
        base: {
          [tokenType]: typeData,
        },
      }
      fs.writeFileSync(filePath, JSON.stringify(fileContent, null, 2))
      console.log(`   ✅ Generated system/base/${fileName}`)
    }
  })

  // Generate alias token files by type and mode
  Object.entries(organizedTokens.alias).forEach(([tokenType, typeData]) => {
    // Check if this type has modes or is a direct token structure
    const hasModesStructure = Object.keys(typeData).some(
      (key) =>
        !key.startsWith("$") &&
        typeof typeData[key] === "object" &&
        typeData[key].$description
    )

    if (hasModesStructure) {
      // Generate separate files for each mode
      Object.entries(typeData).forEach(([modeName, tokens]) => {
        if (!modeName.startsWith("$")) {
          // Special handling for typography style mode - create subfolder
          let fileName, filePath
          if (tokenType === "typography" && modeName === "style") {
            const subDir = path.join(aliasDir, tokenType)
            fs.mkdirSync(subDir, { recursive: true })
            fileName = `${modeName}.json`
            filePath = path.join(subDir, fileName)
          } else {
            fileName = `${tokenType}:${modeName}.json`
            filePath = path.join(aliasDir, fileName)
          }

          // Extract tokens without mode wrapper
          const { $description, ...tokensWithoutMode } = tokens
          const fileContent = {
            alias: {
              [tokenType]: {
                $description,
                ...tokensWithoutMode,
              },
            },
          }
          fs.writeFileSync(filePath, JSON.stringify(fileContent, null, 2))

          // Log with appropriate path
          console.log(`   ✅ Generated system/alias/${fileName}`)
        }
      })
    } else {
      // Generate single file for type (no modes)
      const fileName = `${tokenType}.json`
      const filePath = path.join(aliasDir, fileName)
      const fileContent = {
        alias: {
          [tokenType]: typeData,
        },
      }
      fs.writeFileSync(filePath, JSON.stringify(fileContent, null, 2))
      console.log(`   ✅ Generated system/alias/${fileName}`)
    }
  })
}

/**
 * Main function to process Figma tokens and generate W3C format
 * @param {string} inputDir - Directory containing Figma token files
 * @param {string} outputFile - Output file path for W3C tokens
 */
function main(inputDir = DEFAULT_INPUT_DIR, outputFile = DEFAULT_OUTPUT_FILE) {
  try {
    console.log("🚀 Starting Figma to W3C Design Tokens conversion...")
    console.log(`📁 Input directory: ${inputDir}`)
    console.log(`📄 Output file: ${outputFile}`)

    // Ensure output directory exists
    const outputDir = path.dirname(outputFile)
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }

    let mergedTokens = {
      global: {},
      base: {},
      alias: {},
    }

    let hasProcessedFiles = false

    // Process global folder first
    const globalDir = path.join(inputDir, "global")
    if (fs.existsSync(globalDir)) {
      console.log("📁 Processing global folder...")
      const globalFiles = fs
        .readdirSync(globalDir)
        .filter((file) => file.endsWith(".json"))

      globalFiles.forEach((file) => {
        const filePath = path.join(globalDir, file)
        console.log(`📖 Processing global/${file}...`)

        try {
          const figmaData = JSON.parse(fs.readFileSync(filePath, "utf8"))
          const organizedTokens = convertFigmaToW3C(figmaData, "global")
          hasProcessedFiles = true

          // Merge tokens
          mergeOrganizedTokens(mergedTokens, organizedTokens)

          console.log(`✅ Successfully processed global/${file}`)
        } catch (error) {
          console.error(`❌ Error processing global/${file}:`, error.message)
        }
      })
    }

    // Process system folder
    const systemDir = path.join(inputDir, "system")
    if (fs.existsSync(systemDir)) {
      console.log("📁 Processing system folder...")
      const systemFiles = fs
        .readdirSync(systemDir)
        .filter((file) => file.endsWith(".json"))

      systemFiles.forEach((file) => {
        const filePath = path.join(systemDir, file)
        const fileName = path.basename(file, ".json") // Get filename without extension
        console.log(`📖 Processing system/${file}...`)

        try {
          const figmaData = JSON.parse(fs.readFileSync(filePath, "utf8"))
          const organizedTokens = convertFigmaToW3C(
            figmaData,
            "system",
            fileName
          )
          hasProcessedFiles = true

          // Merge tokens
          mergeOrganizedTokens(mergedTokens, organizedTokens)

          console.log(`✅ Successfully processed system/${file}`)
        } catch (error) {
          console.error(`❌ Error processing system/${file}:`, error.message)
        }
      })
    }

    // Fallback: Process files directly in input directory (for backward compatibility)
    const directFiles = fs
      .readdirSync(inputDir)
      .filter(
        (file) =>
          file.endsWith(".json") &&
          !fs.statSync(path.join(inputDir, file)).isDirectory()
      )

    if (directFiles.length > 0) {
      console.log("📁 Processing files in root directory...")
      directFiles.forEach((file) => {
        const filePath = path.join(inputDir, file)
        console.log(`📖 Processing ${file}...`)

        try {
          const figmaData = JSON.parse(fs.readFileSync(filePath, "utf8"))
          const organizedTokens = convertFigmaToW3C(figmaData)
          hasProcessedFiles = true

          // Merge tokens
          mergeOrganizedTokens(mergedTokens, organizedTokens)

          console.log(`✅ Successfully processed ${file}`)
        } catch (error) {
          console.error(`❌ Error processing ${file}:`, error.message)
        }
      })
    }

    if (!hasProcessedFiles) {
      console.warn("⚠️  No JSON files found in any directory")
      return
    }

    // Write the merged W3C tokens to output file (combined format)
    fs.writeFileSync(outputFile, JSON.stringify(mergedTokens, null, 2))

    // Generate separate files by type and mode
    const baseOutputDir = path.dirname(outputFile)
    const tokensDir = path.join(baseOutputDir, "tokens")
    generateSeparateFiles(mergedTokens, tokensDir)

    console.log("🎉 Conversion completed successfully!")

    // Count types and files
    let globalTypesCount = 0
    let globalFileCount = 0
    Object.entries(mergedTokens.global).forEach(([type, typeData]) => {
      globalTypesCount++
      // Check if this type has modes or is a direct token structure
      const hasModesStructure = Object.keys(typeData).some(
        (key) =>
          !key.startsWith("$") &&
          typeof typeData[key] === "object" &&
          typeData[key].$description
      )

      if (hasModesStructure) {
        globalFileCount += Object.keys(typeData).filter(
          (key) => !key.startsWith("$")
        ).length
      } else {
        globalFileCount += 1 // Single file for this type
      }
    })

    let baseTypesCount = 0
    let baseFileCount = 0
    Object.entries(mergedTokens.base).forEach(([type, typeData]) => {
      baseTypesCount++
      // Check if this type has modes or is a direct token structure
      const hasModesStructure = Object.keys(typeData).some(
        (key) =>
          !key.startsWith("$") &&
          typeof typeData[key] === "object" &&
          typeData[key].$description
      )

      if (hasModesStructure) {
        baseFileCount += Object.keys(typeData).filter(
          (key) => !key.startsWith("$")
        ).length
      } else {
        baseFileCount += 1 // Single file for this type
      }
    })

    let aliasTypesCount = 0
    let aliasFileCount = 0
    Object.entries(mergedTokens.alias).forEach(([type, typeData]) => {
      aliasTypesCount++
      // Check if this type has modes or is a direct token structure
      const hasModesStructure = Object.keys(typeData).some(
        (key) =>
          !key.startsWith("$") &&
          typeof typeData[key] === "object" &&
          typeData[key].$description
      )

      if (hasModesStructure) {
        aliasFileCount += Object.keys(typeData).filter(
          (key) => !key.startsWith("$")
        ).length
      } else {
        aliasFileCount += 1 // Single file for this type
      }
    })

    if (globalTypesCount > 0) {
      console.log(
        `📊 Generated global: ${globalTypesCount} types, ${globalFileCount} files`
      )
    }
    console.log(
      `📊 Generated base: ${baseTypesCount} types, ${baseFileCount} files`
    )
    console.log(
      `📊 Generated alias: ${aliasTypesCount} types, ${aliasFileCount} files`
    )

    // Count total tokens recursively
    function countTokens(obj) {
      let count = 0
      for (const [key, value] of Object.entries(obj)) {
        if (key.startsWith("$")) continue // Skip metadata
        if (value && typeof value === "object" && value.$value !== undefined) {
          count++ // This is a token
        } else if (value && typeof value === "object") {
          count += countTokens(value) // Recurse into nested groups
        }
      }
      return count
    }

    const totalTokens = countTokens(mergedTokens)
    console.log(`🎯 Total tokens: ${totalTokens}`)
  } catch (error) {
    console.error("💥 Fatal error:", error.message)
    process.exit(1)
  }
}

// Run the script if called directly
if (require.main === module) {
  const args = process.argv.slice(2)
  const inputDir = args[0] || DEFAULT_INPUT_DIR
  const outputFile = args[1] || DEFAULT_OUTPUT_FILE

  main(inputDir, outputFile)
}

module.exports = {
  convertFigmaToW3C,
  convertColor,
  convertDimension,
  convertFontWeight,
  sanitizeTokenName,
  main,
}
