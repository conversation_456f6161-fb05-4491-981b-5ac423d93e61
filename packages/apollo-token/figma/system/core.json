{"version": "1.0.4", "metadata": {}, "collections": [{"name": "Alias Color", "modes": [{"name": "Light Mode", "variables": [{"name": "Alias Color/Schemes/primary/primary", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "primary/40"}}, {"name": "Alias Color/Extended/error/Error", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "danger/40"}}, {"name": "Alias Color/Extended/error/On Error", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "danger/100"}}, {"name": "Alias Color/Extended/error/Error Container", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "danger/95"}}, {"name": "Alias Color/Extended/error/On Error Container", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "danger/30"}}, {"name": "Alias Color/Extended/warning/Warning", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "warning/50"}}, {"name": "Alias Color/Extended/warning/On Warning", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "warning/100"}}, {"name": "Alias Color/Extended/warning/Warning Container", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "warning/95"}}, {"name": "Alias Color/Extended/warning/On Warning Container", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "warning/30"}}, {"name": "Alias Color/Schemes/secondary/Seconday", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "secondary/40"}}, {"name": "Alias Color/Schemes/secondary/On Secondary", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "secondary/100"}}, {"name": "Alias Color/Schemes/secondary/Secondary Container", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "secondary/95"}}, {"name": "Alias Color/Schemes/secondary/On Secondary Container", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "secondary/30"}}, {"name": "Alias Color/Schemes/primary/Surface Tint", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "primary/40"}}, {"name": "Alias Color/Schemes/primary/On Primary", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "primary/100"}}, {"name": "Alias Color/Schemes/primary/Primary Container", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "primary/95"}}, {"name": "Alias Color/Schemes/tertiary/Tertiary", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "tertiary/40"}}, {"name": "Alias Color/Schemes/tertiary/On Tertiary", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "tertiary/100"}}, {"name": "Alias Color/Schemes/tertiary/Tertiary Container", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "tertiary/95"}}, {"name": "Alias Color/Schemes/tertiary/On Tertiary Container", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "tertiary/30"}}, {"name": "Alias Color/Schemes/background-and-surface/Background", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "neutral/100"}}, {"name": "Alias Color/Schemes/background-and-surface/On Background", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "neutral/99"}}, {"name": "Alias Color/Schemes/background-and-surface/Surface", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "neutral/99"}}, {"name": "Alias Color/Schemes/background-and-surface/On Surface", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "neutral/30"}}, {"name": "Alias Color/Schemes/background-and-surface/Surface Variant", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "neutral/100"}}, {"name": "Alias Color/Schemes/background-and-surface/On Surface Variant", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "neutral/50"}}, {"name": "Alias Color/Schemes/outline-and-border/Outline", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "neutral/70"}}, {"name": "Alias Color/Schemes/outline-and-border/Outline Variant", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "neutral/80"}}, {"name": "Alias Color/Schemes/outline-and-border/Border", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "neutral/30"}}, {"name": "Alias Color/Schemes/effects/Shadow", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "overlay-black/40"}}, {"name": "Alias Color/Schemes/effects/Scrim", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "overlay-black/40"}}, {"name": "Alias Color/Extended/success/Success", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "success/50"}}, {"name": "Alias Color/Extended/success/On Success", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "success/100"}}, {"name": "Alias Color/Extended/success/Success Container", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "success/95"}}, {"name": "Alias Color/Extended/success/On Success Container", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "success/30"}}]}, {"name": "Dark Mode", "variables": [{"name": "Alias Color/Schemes/primary/primary", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "primary/70"}}, {"name": "Alias Color/Extended/error/Error", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "danger/90"}}, {"name": "Alias Color/Extended/error/On Error", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "danger/20"}}, {"name": "Alias Color/Extended/error/Error Container", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "danger/30"}}, {"name": "Alias Color/Extended/error/On Error Container", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "danger/95"}}, {"name": "Alias Color/Extended/warning/Warning", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "warning/90"}}, {"name": "Alias Color/Extended/warning/On Warning", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "warning/20"}}, {"name": "Alias Color/Extended/warning/Warning Container", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "warning/30"}}, {"name": "Alias Color/Extended/warning/On Warning Container", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "danger/95"}}, {"name": "Alias Color/Schemes/secondary/Seconday", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "secondary/70"}}, {"name": "Alias Color/Schemes/secondary/On Secondary", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "secondary/20"}}, {"name": "Alias Color/Schemes/secondary/Secondary Container", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "secondary/30"}}, {"name": "Alias Color/Schemes/secondary/On Secondary Container", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "secondary/95"}}, {"name": "Alias Color/Schemes/primary/Surface Tint", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "primary/70"}}, {"name": "Alias Color/Schemes/primary/On Primary", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "primary/20"}}, {"name": "Alias Color/Schemes/primary/Primary Container", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "primary/30"}}, {"name": "Alias Color/Schemes/tertiary/Tertiary", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "tertiary/70"}}, {"name": "Alias Color/Schemes/tertiary/On Tertiary", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "tertiary/20"}}, {"name": "Alias Color/Schemes/tertiary/Tertiary Container", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "tertiary/30"}}, {"name": "Alias Color/Schemes/tertiary/On Tertiary Container", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "tertiary/95"}}, {"name": "Alias Color/Schemes/background-and-surface/Background", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "neutral/10"}}, {"name": "Alias Color/Schemes/background-and-surface/On Background", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "neutral/20"}}, {"name": "Alias Color/Schemes/background-and-surface/Surface", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "neutral/10"}}, {"name": "Alias Color/Schemes/background-and-surface/On Surface", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "neutral/99"}}, {"name": "Alias Color/Schemes/background-and-surface/Surface Variant", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "neutral/70"}}, {"name": "Alias Color/Schemes/background-and-surface/On Surface Variant", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "neutral/95"}}, {"name": "Alias Color/Schemes/outline-and-border/Outline", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "neutral/40"}}, {"name": "Alias Color/Schemes/outline-and-border/Outline Variant", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "neutral/80"}}, {"name": "Alias Color/Schemes/outline-and-border/Border", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "neutral/95"}}, {"name": "Alias Color/Schemes/effects/Shadow", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "overlay-black/60"}}, {"name": "Alias Color/Schemes/effects/Scrim", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "overlay-black/60"}}, {"name": "Alias Color/Extended/success/Success", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "primary/90"}}, {"name": "Alias Color/Extended/success/On Success", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "success/20"}}, {"name": "Alias Color/Extended/success/Success Container", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "success/30"}}, {"name": "Alias Color/Extended/success/On Success Container", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "success/95"}}]}]}, {"name": "Base Color ", "modes": [{"name": "Mode 1", "variables": [{"name": "primary/0", "type": "color", "isAlias": true, "value": {"name": "green-pine/0"}}, {"name": "primary/10", "type": "color", "isAlias": true, "value": {"name": "green-pine/10"}}, {"name": "primary/20", "type": "color", "isAlias": true, "value": {"name": "green-pine/20"}}, {"name": "primary/30", "type": "color", "isAlias": true, "value": {"name": "green-pine/30"}}, {"name": "primary/40", "type": "color", "isAlias": true, "value": {"name": "green-pine/40"}}, {"name": "primary/50", "type": "color", "isAlias": true, "value": {"name": "green-pine/50"}}, {"name": "primary/60", "type": "color", "isAlias": true, "value": {"name": "green-pine/60"}}, {"name": "primary/70", "type": "color", "isAlias": true, "value": {"name": "green-pine/70"}}, {"name": "primary/80", "type": "color", "isAlias": true, "value": {"name": "green-pine/80"}}, {"name": "primary/90", "type": "color", "isAlias": true, "value": {"name": "green-pine/90"}}, {"name": "primary/95", "type": "color", "isAlias": true, "value": {"name": "green-pine/95"}}, {"name": "primary/99", "type": "color", "isAlias": true, "value": {"name": "green-pine/99"}}, {"name": "primary/100", "type": "color", "isAlias": true, "value": {"name": "green-pine/100"}}, {"name": "secondary/0", "type": "color", "isAlias": true, "value": {"name": "gray-bluish/0"}}, {"name": "secondary/10", "type": "color", "isAlias": true, "value": {"name": "gray-bluish/10"}}, {"name": "secondary/20", "type": "color", "isAlias": true, "value": {"name": "gray-bluish/20"}}, {"name": "secondary/30", "type": "color", "isAlias": true, "value": {"name": "gray-bluish/30"}}, {"name": "secondary/40", "type": "color", "isAlias": true, "value": {"name": "gray-bluish/40"}}, {"name": "secondary/50", "type": "color", "isAlias": true, "value": {"name": "gray-bluish/50"}}, {"name": "secondary/60", "type": "color", "isAlias": true, "value": {"name": "gray-bluish/60"}}, {"name": "secondary/70", "type": "color", "isAlias": true, "value": {"name": "gray-bluish/70"}}, {"name": "secondary/80", "type": "color", "isAlias": true, "value": {"name": "gray-bluish/80"}}, {"name": "secondary/90", "type": "color", "isAlias": true, "value": {"name": "gray-bluish/90"}}, {"name": "secondary/95", "type": "color", "isAlias": true, "value": {"name": "gray-bluish/95"}}, {"name": "secondary/99", "type": "color", "isAlias": true, "value": {"name": "gray-bluish/99"}}, {"name": "secondary/100", "type": "color", "isAlias": true, "value": {"name": "gray-bluish/100"}}, {"name": "tertiary/0", "type": "color", "isAlias": true, "value": {"name": "blue-ocean/0"}}, {"name": "neutral/0", "type": "color", "isAlias": true, "value": {"name": "gray-smoke/0"}}, {"name": "neutral/10", "type": "color", "isAlias": true, "value": {"name": "gray-smoke/10"}}, {"name": "neutral/20", "type": "color", "isAlias": true, "value": {"name": "gray-smoke/20"}}, {"name": "neutral/30", "type": "color", "isAlias": true, "value": {"name": "gray-smoke/30"}}, {"name": "neutral/40", "type": "color", "isAlias": true, "value": {"name": "gray-smoke/40"}}, {"name": "neutral/50", "type": "color", "isAlias": true, "value": {"name": "gray-smoke/50"}}, {"name": "neutral/60", "type": "color", "isAlias": true, "value": {"name": "gray-smoke/60"}}, {"name": "neutral/70", "type": "color", "isAlias": true, "value": {"name": "gray-smoke/70"}}, {"name": "neutral/80", "type": "color", "isAlias": true, "value": {"name": "gray-smoke/80"}}, {"name": "neutral/90", "type": "color", "isAlias": true, "value": {"name": "gray-smoke/90"}}, {"name": "neutral/95", "type": "color", "isAlias": true, "value": {"name": "gray-smoke/95"}}, {"name": "neutral/99", "type": "color", "isAlias": true, "value": {"name": "gray-smoke/99"}}, {"name": "neutral/100", "type": "color", "isAlias": true, "value": {"name": "gray-smoke/100"}}, {"name": "tertiary/10", "type": "color", "isAlias": true, "value": {"name": "blue-ocean/10"}}, {"name": "tertiary/20", "type": "color", "isAlias": true, "value": {"name": "blue-ocean/20"}}, {"name": "tertiary/30", "type": "color", "isAlias": true, "value": {"name": "blue-ocean/30"}}, {"name": "tertiary/40", "type": "color", "isAlias": true, "value": {"name": "blue-ocean/40"}}, {"name": "tertiary/50", "type": "color", "isAlias": true, "value": {"name": "blue-ocean/50"}}, {"name": "tertiary/60", "type": "color", "isAlias": true, "value": {"name": "blue-ocean/60"}}, {"name": "tertiary/70", "type": "color", "isAlias": true, "value": {"name": "blue-ocean/70"}}, {"name": "tertiary/80", "type": "color", "isAlias": true, "value": {"name": "blue-ocean/80"}}, {"name": "tertiary/90", "type": "color", "isAlias": true, "value": {"name": "blue-ocean/90"}}, {"name": "tertiary/95", "type": "color", "isAlias": true, "value": {"name": "blue-ocean/95"}}, {"name": "tertiary/99", "type": "color", "isAlias": true, "value": {"name": "blue-ocean/99"}}, {"name": "tertiary/100", "type": "color", "isAlias": true, "value": {"name": "blue-ocean/100"}}, {"name": "danger/0", "type": "color", "isAlias": true, "value": {"name": "red-cherry/0"}}, {"name": "danger/10", "type": "color", "isAlias": true, "value": {"name": "red-cherry/10"}}, {"name": "danger/20", "type": "color", "isAlias": true, "value": {"name": "red-cherry/20"}}, {"name": "danger/30", "type": "color", "isAlias": true, "value": {"name": "red-cherry/30"}}, {"name": "danger/40", "type": "color", "isAlias": true, "value": {"name": "red-cherry/40"}}, {"name": "danger/50", "type": "color", "isAlias": true, "value": {"name": "red-cherry/50"}}, {"name": "danger/60", "type": "color", "isAlias": true, "value": {"name": "red-cherry/60"}}, {"name": "danger/70", "type": "color", "isAlias": true, "value": {"name": "red-cherry/70"}}, {"name": "danger/80", "type": "color", "isAlias": true, "value": {"name": "red-cherry/80"}}, {"name": "danger/90", "type": "color", "isAlias": true, "value": {"name": "red-cherry/90"}}, {"name": "danger/95", "type": "color", "isAlias": true, "value": {"name": "red-cherry/95"}}, {"name": "danger/99", "type": "color", "isAlias": true, "value": {"name": "red-cherry/99"}}, {"name": "danger/100", "type": "color", "isAlias": true, "value": {"name": "red-cherry/100"}}, {"name": "warning/0", "type": "color", "isAlias": true, "value": {"name": "yellow-peanut/0"}}, {"name": "warning/10", "type": "color", "isAlias": true, "value": {"name": "yellow-peanut/10"}}, {"name": "warning/20", "type": "color", "isAlias": true, "value": {"name": "yellow-peanut/20"}}, {"name": "warning/30", "type": "color", "isAlias": true, "value": {"name": "yellow-peanut/30"}}, {"name": "warning/40", "type": "color", "isAlias": true, "value": {"name": "yellow-peanut/40"}}, {"name": "warning/50", "type": "color", "isAlias": true, "value": {"name": "yellow-peanut/50"}}, {"name": "warning/60", "type": "color", "isAlias": true, "value": {"name": "yellow-peanut/60"}}, {"name": "warning/70", "type": "color", "isAlias": true, "value": {"name": "yellow-peanut/70"}}, {"name": "warning/80", "type": "color", "isAlias": true, "value": {"name": "yellow-peanut/80"}}, {"name": "warning/90", "type": "color", "isAlias": true, "value": {"name": "yellow-peanut/90"}}, {"name": "warning/95", "type": "color", "isAlias": true, "value": {"name": "yellow-peanut/95"}}, {"name": "warning/99", "type": "color", "isAlias": true, "value": {"name": "yellow-peanut/99"}}, {"name": "warning/100", "type": "color", "isAlias": true, "value": {"name": "yellow-peanut/100"}}, {"name": "success/0", "type": "color", "isAlias": true, "value": {"name": "green-matcha/0"}}, {"name": "success/10", "type": "color", "isAlias": true, "value": {"name": "green-matcha/10"}}, {"name": "success/20", "type": "color", "isAlias": true, "value": {"name": "green-matcha/20"}}, {"name": "success/30", "type": "color", "isAlias": true, "value": {"name": "green-matcha/30"}}, {"name": "success/40", "type": "color", "isAlias": true, "value": {"name": "green-matcha/40"}}, {"name": "success/50", "type": "color", "isAlias": true, "value": {"name": "green-matcha/50"}}, {"name": "success/60", "type": "color", "isAlias": true, "value": {"name": "green-matcha/60"}}, {"name": "success/70", "type": "color", "isAlias": true, "value": {"name": "green-matcha/70"}}, {"name": "success/80", "type": "color", "isAlias": true, "value": {"name": "green-matcha/80"}}, {"name": "success/90", "type": "color", "isAlias": true, "value": {"name": "green-matcha/90"}}, {"name": "success/95", "type": "color", "isAlias": true, "value": {"name": "green-matcha/95"}}, {"name": "success/99", "type": "color", "isAlias": true, "value": {"name": "green-matcha/99"}}, {"name": "success/100", "type": "color", "isAlias": true, "value": {"name": "green-matcha/100"}}, {"name": "overlay-black/0", "type": "color", "isAlias": true, "value": {"name": "black-soft/0"}}, {"name": "overlay-black/10", "type": "color", "isAlias": true, "value": {"name": "black-soft/10"}}, {"name": "overlay-black/20", "type": "color", "isAlias": true, "value": {"name": "black-soft/20"}}, {"name": "overlay-black/30", "type": "color", "isAlias": true, "value": {"name": "black-soft/30"}}, {"name": "overlay-black/40", "type": "color", "isAlias": true, "value": {"name": "black-soft/40"}}, {"name": "overlay-black/50", "type": "color", "isAlias": true, "value": {"name": "black-soft/50"}}, {"name": "overlay-black/60", "type": "color", "isAlias": true, "value": {"name": "black-soft/60"}}, {"name": "overlay-black/70", "type": "color", "isAlias": true, "value": {"name": "black-soft/70"}}, {"name": "overlay-black/80", "type": "color", "isAlias": true, "value": {"name": "black-soft/80"}}, {"name": "overlay-black/90", "type": "color", "isAlias": true, "value": {"name": "black-soft/90"}}, {"name": "overlay-black/100", "type": "color", "isAlias": true, "value": {"name": "black-soft/100"}}, {"name": "overlay-white/0", "type": "color", "isAlias": true, "value": {"name": "white-soft/0"}}, {"name": "kid-club [ยังไม่ใช้]/0", "type": "color", "isAlias": true, "value": {"name": "lilac-soft/0"}}, {"name": "kid-club [ยังไม่ใช้]/Color", "type": "color", "isAlias": false, "value": "#FFFFFF"}, {"name": "kid-club [ยังไม่ใช้]/Color 2", "type": "color", "isAlias": false, "value": "#FFFFFF"}, {"name": "kid-club [ยังไม่ใช้]/Color 3", "type": "color", "isAlias": false, "value": "#FFFFFF"}, {"name": "kid-club [ยังไม่ใช้]/Color 4", "type": "color", "isAlias": false, "value": "#FFFFFF"}, {"name": "kid-club [ยังไม่ใช้]/Color 5", "type": "color", "isAlias": false, "value": "#FFFFFF"}, {"name": "kid-club [ยังไม่ใช้]/Color 6", "type": "color", "isAlias": false, "value": "#FFFFFF"}, {"name": "kid-club [ยังไม่ใช้]/Color 7", "type": "color", "isAlias": false, "value": "#FFFFFF"}, {"name": "kid-club [ยังไม่ใช้]/Color 8", "type": "color", "isAlias": false, "value": "#FFFFFF"}, {"name": "kid-club [ยังไม่ใช้]/Color 9", "type": "color", "isAlias": false, "value": "#FFFFFF"}, {"name": "kid-club [ยังไม่ใช้]/Color 10", "type": "color", "isAlias": false, "value": "#FFFFFF"}, {"name": "kid-club [ยังไม่ใช้]/Color 11", "type": "color", "isAlias": false, "value": "#FFFFFF"}, {"name": "kid-club [ยังไม่ใช้]/Color 12", "type": "color", "isAlias": false, "value": "#FFFFFF"}, {"name": "overlay-white/10", "type": "color", "isAlias": true, "value": {"name": "white-soft/10"}}, {"name": "overlay-white/20", "type": "color", "isAlias": true, "value": {"name": "white-soft/20"}}, {"name": "overlay-white/30", "type": "color", "isAlias": true, "value": {"name": "white-soft/30"}}, {"name": "overlay-white/40", "type": "color", "isAlias": true, "value": {"name": "white-soft/40"}}, {"name": "overlay-white/50", "type": "color", "isAlias": true, "value": {"name": "white-soft/50"}}, {"name": "overlay-white/60", "type": "color", "isAlias": true, "value": {"name": "white-soft/60"}}, {"name": "overlay-white/70", "type": "color", "isAlias": true, "value": {"name": "white-soft/70"}}, {"name": "overlay-white/80", "type": "color", "isAlias": true, "value": {"name": "white-soft/80"}}, {"name": "overlay-white/90", "type": "color", "isAlias": true, "value": {"name": "white-soft/90"}}, {"name": "overlay-white/100", "type": "color", "isAlias": true, "value": {"name": "white-soft/100"}}]}]}, {"name": "Alias Ty<PERSON>", "modes": [{"name": "Baseline(16px)", "variables": [{"name": "font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "display/large/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "headLine/large/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "title/large/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "body/large/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "label/large/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "label/large/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight1"}}, {"name": "label/large/weight-emphasized", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight2"}}, {"name": "label/large/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/2xs"}}, {"name": "label/large/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height10"}}, {"name": "label/medium/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "label/medium/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight1"}}, {"name": "label/medium/weight-emphasized", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight2"}}, {"name": "label/medium/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/3xs"}}, {"name": "label/medium/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height10"}}, {"name": "label/small/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "label/small/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight1"}}, {"name": "label/small/weight-emphasized", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight2"}}, {"name": "label/small/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/4xs"}}, {"name": "label/small/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height13"}}, {"name": "body/large/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight1"}}, {"name": "body/large/weight-emphasized", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight2"}}, {"name": "body/large/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/xs"}}, {"name": "body/large/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height13"}}, {"name": "body/medium/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "body/medium/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight1"}}, {"name": "body/medium/weight-emphasized", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight2"}}, {"name": "body/medium/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/2xs"}}, {"name": "body/medium/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height13"}}, {"name": "body/small/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "body/small/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight1"}}, {"name": "body/small/weight-emphasized", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight2"}}, {"name": "body/small/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/3xs"}}, {"name": "body/small/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height10"}}, {"name": "title/large/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight2"}}, {"name": "title/large/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/xs-plus"}}, {"name": "title/large/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height21"}}, {"name": "title/medium/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "title/medium/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight2"}}, {"name": "title/medium/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/xs-md"}}, {"name": "title/medium/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height21"}}, {"name": "title/small/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "title/small/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight3"}}, {"name": "title/small/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/xs-sm"}}, {"name": "title/small/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height17"}}, {"name": "headLine/large/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight3"}}, {"name": "headLine/large/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/lg"}}, {"name": "headLine/large/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height29"}}, {"name": "headLine/medium/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "headLine/medium/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight3"}}, {"name": "headLine/medium/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/md"}}, {"name": "headLine/medium/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height44"}}, {"name": "headLine/small/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "headLine/small/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight3"}}, {"name": "headLine/small/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/sm"}}, {"name": "headLine/small/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height21"}}, {"name": "display/large/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight3"}}, {"name": "display/large/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/3xl"}}, {"name": "display/large/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height49"}}, {"name": "display/medium/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "display/medium/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight3"}}, {"name": "display/medium/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/2xl"}}, {"name": "display/medium/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height49"}}, {"name": "display/small/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "display/small/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight3"}}, {"name": "display/small/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/xl"}}, {"name": "display/small/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height31"}}]}, {"name": "Baseline(28px)", "variables": [{"name": "font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "display/large/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "headLine/large/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "title/large/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "body/large/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "label/large/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "label/large/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight1"}}, {"name": "label/large/weight-emphasized", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight2"}}, {"name": "label/large/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/2xs"}}, {"name": "label/large/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height21"}}, {"name": "label/medium/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "label/medium/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight1"}}, {"name": "label/medium/weight-emphasized", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight2"}}, {"name": "label/medium/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/3xs"}}, {"name": "label/medium/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height21"}}, {"name": "label/small/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "label/small/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight1"}}, {"name": "label/small/weight-emphasized", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight2"}}, {"name": "label/small/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/4xs"}}, {"name": "label/small/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height13"}}, {"name": "body/large/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight1"}}, {"name": "body/large/weight-emphasized", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight2"}}, {"name": "body/large/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/xs"}}, {"name": "body/large/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height25"}}, {"name": "body/medium/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "body/medium/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight1"}}, {"name": "body/medium/weight-emphasized", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight2"}}, {"name": "body/medium/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/2xs"}}, {"name": "body/medium/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height21"}}, {"name": "body/small/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "body/small/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight1"}}, {"name": "body/small/weight-emphasized", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight2"}}, {"name": "body/small/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/3xs"}}, {"name": "body/small/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height21"}}, {"name": "title/large/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight2"}}, {"name": "title/large/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/xs-plus"}}, {"name": "title/large/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height43"}}, {"name": "title/medium/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "title/medium/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight2"}}, {"name": "title/medium/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/xs-md"}}, {"name": "title/medium/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height31"}}, {"name": "title/small/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "title/small/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight3"}}, {"name": "title/small/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/xs-sm"}}, {"name": "title/small/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height29"}}, {"name": "headLine/large/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight3"}}, {"name": "headLine/large/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/lg"}}, {"name": "headLine/large/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height49"}}, {"name": "headLine/medium/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "headLine/medium/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight3"}}, {"name": "headLine/medium/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/md"}}, {"name": "headLine/medium/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height43"}}, {"name": "headLine/small/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "headLine/small/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight3"}}, {"name": "headLine/small/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/sm"}}, {"name": "headLine/small/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height43"}}, {"name": "display/large/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight3"}}, {"name": "display/large/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/3xl"}}, {"name": "display/large/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height50"}}, {"name": "display/medium/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "display/medium/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight3"}}, {"name": "display/medium/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/2xl"}}, {"name": "display/medium/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height50"}}, {"name": "display/small/font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "display/small/font-weight", "type": "string", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-weight/weight3"}}, {"name": "display/small/font-size", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "font-size/xl"}}, {"name": "display/small/line-height", "type": "number", "isAlias": true, "value": {"collection": "Base Typography", "name": "line-height/line-height49"}}]}]}, {"name": "Base Spacing", "modes": [{"name": "Mode 1", "variables": [{"name": "space1", "type": "number", "isAlias": true, "value": {"name": "none"}}, {"name": "space2", "type": "number", "isAlias": true, "value": {"name": "2"}}, {"name": "space3", "type": "number", "isAlias": true, "value": {"name": "4"}}, {"name": "space4", "type": "number", "isAlias": true, "value": {"name": "6"}}, {"name": "space5", "type": "number", "isAlias": true, "value": {"name": "8"}}, {"name": "space6", "type": "number", "isAlias": true, "value": {"name": "10"}}, {"name": "space7", "type": "number", "isAlias": true, "value": {"name": "12"}}, {"name": "space8", "type": "number", "isAlias": true, "value": {"name": "16"}}, {"name": "space9", "type": "number", "isAlias": true, "value": {"name": "20"}}, {"name": "space10", "type": "number", "isAlias": true, "value": {"name": "24"}}, {"name": "space11", "type": "number", "isAlias": true, "value": {"name": "28"}}, {"name": "space12", "type": "number", "isAlias": true, "value": {"name": "32"}}, {"name": "space13", "type": "number", "isAlias": true, "value": {"name": "36"}}, {"name": "space14", "type": "number", "isAlias": true, "value": {"name": "40"}}, {"name": "space15", "type": "number", "isAlias": true, "value": {"name": "48"}}, {"name": "neg-space1", "type": "number", "isAlias": true, "value": {"name": "-2"}}, {"name": "neg-space2", "type": "number", "isAlias": true, "value": {"name": "-4"}}, {"name": "neg-space3", "type": "number", "isAlias": true, "value": {"name": "-6"}}, {"name": "neg-space4", "type": "number", "isAlias": true, "value": {"name": "-8"}}, {"name": "Alias Color/Schemes/tertiary/Tertiary", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "tertiary/40"}}, {"name": "title/large/font-size", "type": "number", "isAlias": true, "value": {"name": "font-scale/scale8"}}, {"name": "title/large/line-height", "type": "number", "isAlias": true, "value": {"name": "line-height/height8"}}, {"name": "title/large/letter-spacing", "type": "number", "isAlias": true, "value": {"name": "letter-spacing/letter3"}}]}]}, {"name": "Alias Spacing", "modes": [{"name": "Desktop", "variables": [{"name": "margin/vertical/vertical", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space15"}}, {"name": "padding/padding1", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space1"}}, {"name": "gap/gap1", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space1"}}, {"name": "gap/gap2", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space2"}}, {"name": "gap/gap3", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space3"}}, {"name": "gap/gap4", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space4"}}, {"name": "gap/gap5", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space5"}}, {"name": "gap/gap6", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space6"}}, {"name": "gap/gap7", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space7"}}, {"name": "gap/gap8", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space8"}}, {"name": "gap/gap9", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space9"}}, {"name": "gap/gap10", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space10"}}, {"name": "gap/gap11", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space11"}}, {"name": "gap/gap12", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space12"}}, {"name": "padding/padding2", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space2"}}, {"name": "padding/padding3", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space3"}}, {"name": "padding/padding4", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space4"}}, {"name": "padding/padding5", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space5"}}, {"name": "padding/padding6", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space6"}}, {"name": "padding/padding7", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space7"}}, {"name": "padding/padding8", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space8"}}, {"name": "padding/padding9", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space9"}}, {"name": "padding/padding10", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space10"}}, {"name": "padding/padding11", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space11"}}, {"name": "padding/padding12", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space12"}}, {"name": "margin/horizontal/horizontal", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space15"}}]}, {"name": "Mobile", "variables": [{"name": "margin/vertical/vertical", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space8"}}, {"name": "padding/padding1", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space1"}}, {"name": "gap/gap1", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space1"}}, {"name": "gap/gap2", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space2"}}, {"name": "gap/gap3", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space3"}}, {"name": "gap/gap4", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space4"}}, {"name": "gap/gap5", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space5"}}, {"name": "gap/gap6", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space6"}}, {"name": "gap/gap7", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space7"}}, {"name": "gap/gap8", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space8"}}, {"name": "gap/gap9", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space9"}}, {"name": "gap/gap10", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space10"}}, {"name": "gap/gap11", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space11"}}, {"name": "gap/gap12", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space12"}}, {"name": "padding/padding2", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space2"}}, {"name": "padding/padding3", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space3"}}, {"name": "padding/padding4", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space4"}}, {"name": "padding/padding5", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space6"}}, {"name": "padding/padding6", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space6"}}, {"name": "padding/padding7", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space7"}}, {"name": "padding/padding8", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space8"}}, {"name": "padding/padding9", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space9"}}, {"name": "padding/padding10", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space10"}}, {"name": "padding/padding11", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space11"}}, {"name": "padding/padding12", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space12"}}, {"name": "margin/horizontal/horizontal", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space8"}}]}, {"name": "Handheld", "variables": [{"name": "margin/vertical/vertical", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space10"}}, {"name": "padding/padding1", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space1"}}, {"name": "gap/gap1", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space1"}}, {"name": "gap/gap2", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space2"}}, {"name": "gap/gap3", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space3"}}, {"name": "gap/gap4", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space4"}}, {"name": "gap/gap5", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space5"}}, {"name": "gap/gap6", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space6"}}, {"name": "gap/gap7", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space7"}}, {"name": "gap/gap8", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space8"}}, {"name": "gap/gap9", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space9"}}, {"name": "gap/gap10", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space10"}}, {"name": "gap/gap11", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space11"}}, {"name": "gap/gap12", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space12"}}, {"name": "padding/padding2", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space2"}}, {"name": "padding/padding3", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space3"}}, {"name": "padding/padding4", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space4"}}, {"name": "padding/padding5", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space6"}}, {"name": "padding/padding6", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space6"}}, {"name": "padding/padding7", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space7"}}, {"name": "padding/padding8", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space8"}}, {"name": "padding/padding9", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space9"}}, {"name": "padding/padding10", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space10"}}, {"name": "padding/padding11", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space11"}}, {"name": "padding/padding12", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space12"}}, {"name": "margin/horizontal/horizontal", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space10"}}]}]}, {"name": "Base Typography", "modes": [{"name": "Baseline (16px)", "variables": [{"name": "font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "font-size/3xl", "type": "number", "isAlias": true, "value": {"name": "font-size/57"}}, {"name": "font-weight/weight1", "type": "string", "isAlias": true, "value": {"name": "font-weight/regular"}}, {"name": "line-height/line-height1", "type": "number", "isAlias": true, "value": {"name": "line-height/4"}}, {"name": "line-height/line-height2", "type": "number", "isAlias": true, "value": {"name": "line-height/6"}}, {"name": "line-height/line-height3", "type": "number", "isAlias": true, "value": {"name": "line-height/8"}}, {"name": "line-height/line-height4", "type": "number", "isAlias": true, "value": {"name": "line-height/10"}}, {"name": "line-height/line-height5", "type": "number", "isAlias": true, "value": {"name": "line-height/12"}}, {"name": "line-height/line-height6", "type": "number", "isAlias": true, "value": {"name": "line-height/14"}}, {"name": "line-height/line-height7", "type": "number", "isAlias": true, "value": {"name": "line-height/15"}}, {"name": "line-height/line-height8", "type": "number", "isAlias": true, "value": {"name": "line-height/16"}}, {"name": "line-height/line-height9", "type": "number", "isAlias": true, "value": {"name": "line-height/18"}}, {"name": "line-height/line-height10", "type": "number", "isAlias": true, "value": {"name": "line-height/20"}}, {"name": "line-height/line-height11", "type": "number", "isAlias": true, "value": {"name": "line-height/21"}}, {"name": "line-height/line-height12", "type": "number", "isAlias": true, "value": {"name": "line-height/22"}}, {"name": "line-height/line-height13", "type": "number", "isAlias": true, "value": {"name": "line-height/24"}}, {"name": "line-height/line-height14", "type": "number", "isAlias": true, "value": {"name": "line-height/26"}}, {"name": "line-height/line-height15", "type": "number", "isAlias": true, "value": {"name": "line-height/27"}}, {"name": "line-height/line-height16", "type": "number", "isAlias": true, "value": {"name": "line-height/28"}}, {"name": "line-height/line-height17", "type": "number", "isAlias": true, "value": {"name": "line-height/30"}}, {"name": "line-height/line-height18", "type": "number", "isAlias": true, "value": {"name": "line-height/32"}}, {"name": "line-height/line-height19", "type": "number", "isAlias": true, "value": {"name": "line-height/33"}}, {"name": "line-height/line-height20", "type": "number", "isAlias": true, "value": {"name": "line-height/34"}}, {"name": "line-height/line-height21", "type": "number", "isAlias": true, "value": {"name": "line-height/36"}}, {"name": "line-height/line-height22", "type": "number", "isAlias": true, "value": {"name": "line-height/38"}}, {"name": "line-height/line-height23", "type": "number", "isAlias": true, "value": {"name": "line-height/40"}}, {"name": "line-height/line-height24", "type": "number", "isAlias": true, "value": {"name": "line-height/42"}}, {"name": "line-height/line-height25", "type": "number", "isAlias": true, "value": {"name": "line-height/44"}}, {"name": "line-height/line-height26", "type": "number", "isAlias": true, "value": {"name": "line-height/46"}}, {"name": "line-height/line-height27", "type": "number", "isAlias": true, "value": {"name": "line-height/48"}}, {"name": "line-height/line-height28", "type": "number", "isAlias": true, "value": {"name": "line-height/50"}}, {"name": "line-height/line-height29", "type": "number", "isAlias": true, "value": {"name": "line-height/52"}}, {"name": "line-height/line-height30", "type": "number", "isAlias": true, "value": {"name": "line-height/54"}}, {"name": "line-height/line-height31", "type": "number", "isAlias": true, "value": {"name": "line-height/56"}}, {"name": "line-height/line-height32", "type": "number", "isAlias": true, "value": {"name": "line-height/58"}}, {"name": "line-height/line-height33", "type": "number", "isAlias": true, "value": {"name": "line-height/60"}}, {"name": "line-height/line-height34", "type": "number", "isAlias": true, "value": {"name": "line-height/62"}}, {"name": "line-height/line-height35", "type": "number", "isAlias": true, "value": {"name": "line-height/64"}}, {"name": "line-height/line-height36", "type": "number", "isAlias": true, "value": {"name": "line-height/66"}}, {"name": "line-height/line-height37", "type": "number", "isAlias": true, "value": {"name": "line-height/67"}}, {"name": "line-height/line-height38", "type": "number", "isAlias": true, "value": {"name": "line-height/68"}}, {"name": "line-height/line-height39", "type": "number", "isAlias": true, "value": {"name": "line-height/70"}}, {"name": "line-height/line-height40", "type": "number", "isAlias": true, "value": {"name": "line-height/72"}}, {"name": "line-height/line-height41", "type": "number", "isAlias": true, "value": {"name": "line-height/74"}}, {"name": "line-height/line-height42", "type": "number", "isAlias": true, "value": {"name": "line-height/76"}}, {"name": "line-height/line-height43", "type": "number", "isAlias": true, "value": {"name": "line-height/78"}}, {"name": "line-height/line-height44", "type": "number", "isAlias": true, "value": {"name": "line-height/80"}}, {"name": "line-height/line-height45", "type": "number", "isAlias": true, "value": {"name": "line-height/82"}}, {"name": "line-height/line-height46", "type": "number", "isAlias": true, "value": {"name": "line-height/84"}}, {"name": "line-height/line-height47", "type": "number", "isAlias": true, "value": {"name": "line-height/85"}}, {"name": "line-height/line-height48", "type": "number", "isAlias": true, "value": {"name": "line-height/86"}}, {"name": "line-height/line-height49", "type": "number", "isAlias": true, "value": {"name": "line-height/88"}}, {"name": "line-height/line-height50", "type": "number", "isAlias": true, "value": {"name": "line-height/96"}}, {"name": "line-height/line-height51", "type": "number", "isAlias": true, "value": {"name": "line-height/132"}}, {"name": "font-weight/weight2", "type": "string", "isAlias": true, "value": {"name": "font-weight/medium"}}, {"name": "font-weight/weight3", "type": "string", "isAlias": true, "value": {"name": "font-weight/bold"}}, {"name": "font-size/2xl", "type": "number", "isAlias": true, "value": {"name": "font-size/45"}}, {"name": "font-size/xl", "type": "number", "isAlias": true, "value": {"name": "font-size/36"}}, {"name": "font-size/lg", "type": "number", "isAlias": true, "value": {"name": "font-size/32"}}, {"name": "font-size/md", "type": "number", "isAlias": true, "value": {"name": "font-size/28"}}, {"name": "font-size/sm", "type": "number", "isAlias": true, "value": {"name": "font-size/24"}}, {"name": "font-size/xs-plus", "type": "number", "isAlias": true, "value": {"name": "font-size/22"}}, {"name": "font-size/xs-md", "type": "number", "isAlias": true, "value": {"name": "font-size/20"}}, {"name": "font-size/xs-sm", "type": "number", "isAlias": true, "value": {"name": "font-size/18"}}, {"name": "font-size/xs", "type": "number", "isAlias": true, "value": {"name": "font-size/16"}}, {"name": "font-size/2xs", "type": "number", "isAlias": true, "value": {"name": "font-size/14"}}, {"name": "font-size/3xs", "type": "number", "isAlias": true, "value": {"name": "font-size/12"}}, {"name": "font-size/4xs", "type": "number", "isAlias": true, "value": {"name": "font-size/10"}}]}, {"name": "Baseline(28px)", "variables": [{"name": "font-family", "type": "string", "isAlias": true, "value": {"name": "font-family/IBM Plex Sans Thai"}}, {"name": "font-size/3xl", "type": "number", "isAlias": true, "value": {"name": "font-size/64"}}, {"name": "font-weight/weight1", "type": "string", "isAlias": true, "value": {"name": "font-weight/regular"}}, {"name": "line-height/line-height1", "type": "number", "isAlias": true, "value": {"name": "line-height/4"}}, {"name": "line-height/line-height2", "type": "number", "isAlias": true, "value": {"name": "line-height/6"}}, {"name": "line-height/line-height3", "type": "number", "isAlias": true, "value": {"name": "line-height/8"}}, {"name": "line-height/line-height4", "type": "number", "isAlias": true, "value": {"name": "line-height/10"}}, {"name": "line-height/line-height5", "type": "number", "isAlias": true, "value": {"name": "line-height/12"}}, {"name": "line-height/line-height6", "type": "number", "isAlias": true, "value": {"name": "line-height/14"}}, {"name": "line-height/line-height7", "type": "number", "isAlias": true, "value": {"name": "line-height/15"}}, {"name": "line-height/line-height8", "type": "number", "isAlias": true, "value": {"name": "line-height/16"}}, {"name": "line-height/line-height9", "type": "number", "isAlias": true, "value": {"name": "line-height/18"}}, {"name": "line-height/line-height10", "type": "number", "isAlias": true, "value": {"name": "line-height/20"}}, {"name": "line-height/line-height11", "type": "number", "isAlias": true, "value": {"name": "line-height/21"}}, {"name": "line-height/line-height12", "type": "number", "isAlias": true, "value": {"name": "line-height/22"}}, {"name": "line-height/line-height13", "type": "number", "isAlias": true, "value": {"name": "line-height/24"}}, {"name": "line-height/line-height14", "type": "number", "isAlias": true, "value": {"name": "line-height/26"}}, {"name": "line-height/line-height15", "type": "number", "isAlias": true, "value": {"name": "line-height/27"}}, {"name": "line-height/line-height16", "type": "number", "isAlias": true, "value": {"name": "line-height/28"}}, {"name": "line-height/line-height17", "type": "number", "isAlias": true, "value": {"name": "line-height/30"}}, {"name": "line-height/line-height18", "type": "number", "isAlias": true, "value": {"name": "line-height/32"}}, {"name": "line-height/line-height19", "type": "number", "isAlias": true, "value": {"name": "line-height/33"}}, {"name": "line-height/line-height20", "type": "number", "isAlias": true, "value": {"name": "line-height/34"}}, {"name": "line-height/line-height21", "type": "number", "isAlias": true, "value": {"name": "line-height/36"}}, {"name": "line-height/line-height22", "type": "number", "isAlias": true, "value": {"name": "line-height/38"}}, {"name": "line-height/line-height23", "type": "number", "isAlias": true, "value": {"name": "line-height/40"}}, {"name": "line-height/line-height24", "type": "number", "isAlias": true, "value": {"name": "line-height/42"}}, {"name": "line-height/line-height25", "type": "number", "isAlias": true, "value": {"name": "line-height/44"}}, {"name": "line-height/line-height26", "type": "number", "isAlias": true, "value": {"name": "line-height/46"}}, {"name": "line-height/line-height27", "type": "number", "isAlias": true, "value": {"name": "line-height/48"}}, {"name": "line-height/line-height28", "type": "number", "isAlias": true, "value": {"name": "line-height/50"}}, {"name": "line-height/line-height29", "type": "number", "isAlias": true, "value": {"name": "line-height/52"}}, {"name": "line-height/line-height30", "type": "number", "isAlias": true, "value": {"name": "line-height/54"}}, {"name": "line-height/line-height31", "type": "number", "isAlias": true, "value": {"name": "line-height/56"}}, {"name": "line-height/line-height32", "type": "number", "isAlias": true, "value": {"name": "line-height/58"}}, {"name": "line-height/line-height33", "type": "number", "isAlias": true, "value": {"name": "line-height/60"}}, {"name": "line-height/line-height34", "type": "number", "isAlias": true, "value": {"name": "line-height/62"}}, {"name": "line-height/line-height35", "type": "number", "isAlias": true, "value": {"name": "line-height/64"}}, {"name": "line-height/line-height36", "type": "number", "isAlias": true, "value": {"name": "line-height/66"}}, {"name": "line-height/line-height37", "type": "number", "isAlias": true, "value": {"name": "line-height/67"}}, {"name": "line-height/line-height38", "type": "number", "isAlias": true, "value": {"name": "line-height/68"}}, {"name": "line-height/line-height39", "type": "number", "isAlias": true, "value": {"name": "line-height/70"}}, {"name": "line-height/line-height40", "type": "number", "isAlias": true, "value": {"name": "line-height/72"}}, {"name": "line-height/line-height41", "type": "number", "isAlias": true, "value": {"name": "line-height/74"}}, {"name": "line-height/line-height42", "type": "number", "isAlias": true, "value": {"name": "line-height/76"}}, {"name": "line-height/line-height43", "type": "number", "isAlias": true, "value": {"name": "line-height/78"}}, {"name": "line-height/line-height44", "type": "number", "isAlias": true, "value": {"name": "line-height/80"}}, {"name": "line-height/line-height45", "type": "number", "isAlias": true, "value": {"name": "line-height/82"}}, {"name": "line-height/line-height46", "type": "number", "isAlias": true, "value": {"name": "line-height/84"}}, {"name": "line-height/line-height47", "type": "number", "isAlias": true, "value": {"name": "line-height/85"}}, {"name": "line-height/line-height48", "type": "number", "isAlias": true, "value": {"name": "line-height/86"}}, {"name": "line-height/line-height49", "type": "number", "isAlias": true, "value": {"name": "line-height/88"}}, {"name": "line-height/line-height50", "type": "number", "isAlias": true, "value": {"name": "line-height/96"}}, {"name": "line-height/line-height51", "type": "number", "isAlias": true, "value": {"name": "line-height/132"}}, {"name": "font-weight/weight2", "type": "string", "isAlias": true, "value": {"name": "font-weight/medium"}}, {"name": "font-weight/weight3", "type": "string", "isAlias": true, "value": {"name": "font-weight/bold"}}, {"name": "font-size/2xl", "type": "number", "isAlias": true, "value": {"name": "font-size/60"}}, {"name": "font-size/xl", "type": "number", "isAlias": true, "value": {"name": "font-size/56"}}, {"name": "font-size/lg", "type": "number", "isAlias": true, "value": {"name": "font-size/52"}}, {"name": "font-size/md", "type": "number", "isAlias": true, "value": {"name": "font-size/48"}}, {"name": "font-size/sm", "type": "number", "isAlias": true, "value": {"name": "font-size/44"}}, {"name": "font-size/xs-plus", "type": "number", "isAlias": true, "value": {"name": "font-size/40"}}, {"name": "font-size/xs-md", "type": "number", "isAlias": true, "value": {"name": "font-size/36"}}, {"name": "font-size/xs-sm", "type": "number", "isAlias": true, "value": {"name": "font-size/32"}}, {"name": "font-size/xs", "type": "number", "isAlias": true, "value": {"name": "font-size/28"}}, {"name": "font-size/2xs", "type": "number", "isAlias": true, "value": {"name": "font-size/24"}}, {"name": "font-size/3xs", "type": "number", "isAlias": true, "value": {"name": "font-size/20"}}, {"name": "font-size/4xs", "type": "number", "isAlias": true, "value": {"name": "font-size/16"}}]}]}, {"name": "Base Radius", "modes": [{"name": "Mode 1", "variables": [{"name": "none", "type": "number", "isAlias": true, "value": {"name": "none"}}, {"name": "2", "type": "number", "isAlias": true, "value": {"name": "2"}}, {"name": "4", "type": "number", "isAlias": true, "value": {"name": "4"}}, {"name": "6", "type": "number", "isAlias": true, "value": {"name": "6"}}, {"name": "8", "type": "number", "isAlias": true, "value": {"name": "8"}}, {"name": "10", "type": "number", "isAlias": true, "value": {"name": "10"}}, {"name": "12", "type": "number", "isAlias": true, "value": {"name": "12"}}, {"name": "14", "type": "number", "isAlias": true, "value": {"name": "14"}}, {"name": "16", "type": "number", "isAlias": true, "value": {"name": "16"}}, {"name": "18", "type": "number", "isAlias": true, "value": {"name": "18"}}, {"name": "20", "type": "number", "isAlias": true, "value": {"name": "20"}}, {"name": "22", "type": "number", "isAlias": true, "value": {"name": "22"}}, {"name": "24", "type": "number", "isAlias": true, "value": {"name": "24"}}, {"name": "26", "type": "number", "isAlias": true, "value": {"name": "26"}}, {"name": "28", "type": "number", "isAlias": true, "value": {"name": "28"}}, {"name": "30", "type": "number", "isAlias": true, "value": {"name": "30"}}, {"name": "32", "type": "number", "isAlias": true, "value": {"name": "32"}}, {"name": "34", "type": "number", "isAlias": true, "value": {"name": "34"}}, {"name": "36", "type": "number", "isAlias": true, "value": {"name": "36"}}, {"name": "38", "type": "number", "isAlias": true, "value": {"name": "38"}}, {"name": "40", "type": "number", "isAlias": true, "value": {"name": "40"}}, {"name": "42", "type": "number", "isAlias": true, "value": {"name": "42"}}, {"name": "44", "type": "number", "isAlias": true, "value": {"name": "44"}}, {"name": "46", "type": "number", "isAlias": true, "value": {"name": "46"}}, {"name": "48", "type": "number", "isAlias": true, "value": {"name": "48"}}, {"name": "50", "type": "number", "isAlias": true, "value": {"name": "50"}}, {"name": "52", "type": "number", "isAlias": true, "value": {"name": "52"}}, {"name": "54", "type": "number", "isAlias": true, "value": {"name": "54"}}, {"name": "56", "type": "number", "isAlias": true, "value": {"name": "56"}}, {"name": "58", "type": "number", "isAlias": true, "value": {"name": "58"}}, {"name": "60", "type": "number", "isAlias": true, "value": {"name": "60"}}, {"name": "62", "type": "number", "isAlias": true, "value": {"name": "62"}}, {"name": "64", "type": "number", "isAlias": true, "value": {"name": "64"}}, {"name": "66", "type": "number", "isAlias": true, "value": {"name": "66"}}, {"name": "68", "type": "number", "isAlias": true, "value": {"name": "68"}}, {"name": "70", "type": "number", "isAlias": true, "value": {"name": "70"}}, {"name": "72", "type": "number", "isAlias": true, "value": {"name": "72"}}, {"name": "74", "type": "number", "isAlias": true, "value": {"name": "74"}}, {"name": "76", "type": "number", "isAlias": true, "value": {"name": "76"}}, {"name": "78", "type": "number", "isAlias": true, "value": {"name": "78"}}, {"name": "80", "type": "number", "isAlias": true, "value": {"name": "80"}}, {"name": "full", "type": "number", "isAlias": true, "value": {"name": "full"}}]}]}, {"name": "<PERSON><PERSON>", "modes": [{"name": "Mode 1", "variables": [{"name": "radius1", "type": "number", "isAlias": true, "value": {"collection": "Base Radius", "name": "none"}}, {"name": "radius2", "type": "number", "isAlias": true, "value": {"collection": "Base Radius", "name": "4"}}, {"name": "radius3", "type": "number", "isAlias": true, "value": {"collection": "Base Radius", "name": "6"}}, {"name": "radius4", "type": "number", "isAlias": true, "value": {"collection": "Base Radius", "name": "8"}}, {"name": "radius5", "type": "number", "isAlias": true, "value": {"collection": "Base Radius", "name": "10"}}, {"name": "radius6", "type": "number", "isAlias": true, "value": {"collection": "Base Radius", "name": "12"}}, {"name": "radius7", "type": "number", "isAlias": true, "value": {"collection": "Base Radius", "name": "14"}}, {"name": "radius8", "type": "number", "isAlias": true, "value": {"collection": "Base Radius", "name": "16"}}, {"name": "radius9", "type": "number", "isAlias": true, "value": {"collection": "Base Radius", "name": "20"}}, {"name": "radius10", "type": "number", "isAlias": true, "value": {"collection": "Base Radius", "name": "24"}}, {"name": "radius11", "type": "number", "isAlias": true, "value": {"collection": "Base Radius", "name": "full"}}]}]}, {"name": "Base Elevations", "modes": [{"name": "Mode 1", "variables": [{"name": "X-Axis/none", "type": "number", "isAlias": true, "value": {"name": "X-Axis/none"}}, {"name": "X-Axis/1", "type": "number", "isAlias": true, "value": {"name": "X-Axis/1"}}, {"name": "X-Axis/2", "type": "number", "isAlias": true, "value": {"name": "X-Axis/2"}}, {"name": "X-Axis/4", "type": "number", "isAlias": true, "value": {"name": "X-Axis/4"}}, {"name": "X-Axis/6", "type": "number", "isAlias": true, "value": {"name": "X-Axis/6"}}, {"name": "X-Axis/8", "type": "number", "isAlias": true, "value": {"name": "X-Axis/8"}}, {"name": "X-Axis/10", "type": "number", "isAlias": true, "value": {"name": "X-Axis/10"}}, {"name": "X-Axis/12", "type": "number", "isAlias": true, "value": {"name": "X-Axis/12"}}, {"name": "X-Axis/14", "type": "number", "isAlias": true, "value": {"name": "X-Axis/14"}}, {"name": "X-Axis/16", "type": "number", "isAlias": true, "value": {"name": "X-Axis/16"}}, {"name": "X-Axis/18", "type": "number", "isAlias": true, "value": {"name": "X-Axis/18"}}, {"name": "X-Axis/20", "type": "number", "isAlias": true, "value": {"name": "X-Axis/20"}}, {"name": "X-Axis/22", "type": "number", "isAlias": true, "value": {"name": "Y-Axis/22"}}, {"name": "X-Axis/24", "type": "number", "isAlias": true, "value": {"name": "X-Axis/24"}}, {"name": "Y-Axis/none", "type": "number", "isAlias": true, "value": {"name": "Y-Axis/none"}}, {"name": "Y-Axis/1", "type": "number", "isAlias": true, "value": {"name": "Y-Axis/1"}}, {"name": "Y-Axis/2", "type": "number", "isAlias": true, "value": {"name": "Y-Axis/2"}}, {"name": "Y-Axis/4", "type": "number", "isAlias": true, "value": {"name": "Y-Axis/4"}}, {"name": "Y-Axis/6", "type": "number", "isAlias": true, "value": {"name": "Y-Axis/6"}}, {"name": "Y-Axis/8", "type": "number", "isAlias": true, "value": {"name": "Y-Axis/8"}}, {"name": "Y-Axis/10", "type": "number", "isAlias": true, "value": {"name": "Y-Axis/10"}}, {"name": "Y-Axis/12", "type": "number", "isAlias": true, "value": {"name": "Y-Axis/12"}}, {"name": "Y-Axis/14", "type": "number", "isAlias": true, "value": {"name": "Y-Axis/14"}}, {"name": "Y-Axis/16", "type": "number", "isAlias": true, "value": {"name": "Y-Axis/16"}}, {"name": "Y-Axis/18", "type": "number", "isAlias": true, "value": {"name": "Y-Axis/18"}}, {"name": "Y-Axis/20", "type": "number", "isAlias": true, "value": {"name": "Y-Axis/20"}}, {"name": "Y-Axis/22", "type": "number", "isAlias": true, "value": {"name": "Y-Axis/22"}}, {"name": "Y-Axis/24", "type": "number", "isAlias": true, "value": {"name": "Y-Axis/24"}}, {"name": "Spread/none", "type": "number", "isAlias": true, "value": {"name": "Spread/none"}}, {"name": "Spread/1", "type": "number", "isAlias": true, "value": {"name": "Spread/1"}}, {"name": "Spread/2", "type": "number", "isAlias": true, "value": {"name": "Spread/2"}}, {"name": "Spread/4", "type": "number", "isAlias": true, "value": {"name": "Spread/4"}}, {"name": "Spread/6", "type": "number", "isAlias": true, "value": {"name": "Spread/8"}}, {"name": "Spread/8", "type": "number", "isAlias": true, "value": {"name": "Spread/8"}}, {"name": "Spread/10", "type": "number", "isAlias": true, "value": {"name": "Spread/10"}}, {"name": "Spread/12", "type": "number", "isAlias": true, "value": {"name": "Spread/12"}}, {"name": "Spread/14", "type": "number", "isAlias": true, "value": {"name": "Spread/14"}}, {"name": "Spread/16", "type": "number", "isAlias": true, "value": {"name": "Spread/16"}}, {"name": "Spread/18", "type": "number", "isAlias": true, "value": {"name": "Spread/18"}}, {"name": "Spread/20", "type": "number", "isAlias": true, "value": {"name": "Spread/20"}}, {"name": "Spread/22", "type": "number", "isAlias": true, "value": {"name": "Spread/22"}}, {"name": "Spread/24", "type": "number", "isAlias": true, "value": {"name": "Spread/24"}}, {"name": "Blur/none", "type": "number", "isAlias": true, "value": {"name": "Blur/none"}}, {"name": "Blur/2", "type": "number", "isAlias": true, "value": {"name": "Blur/2"}}, {"name": "Blur/4", "type": "number", "isAlias": true, "value": {"name": "Blur/4"}}, {"name": "Blur/6", "type": "number", "isAlias": true, "value": {"name": "Blur/6"}}, {"name": "Blur/8", "type": "number", "isAlias": true, "value": {"name": "Blur/8"}}, {"name": "Blur/10", "type": "number", "isAlias": true, "value": {"name": "Blur/10"}}, {"name": "Blur/12", "type": "number", "isAlias": true, "value": {"name": "Blur/12"}}, {"name": "Blur/14", "type": "number", "isAlias": true, "value": {"name": "Blur/14"}}, {"name": "Blur/16", "type": "number", "isAlias": true, "value": {"name": "Blur/16"}}, {"name": "Blur/18", "type": "number", "isAlias": true, "value": {"name": "Blur/18"}}, {"name": "Blur/20", "type": "number", "isAlias": true, "value": {"name": "Blur/20"}}, {"name": "Blur/22", "type": "number", "isAlias": true, "value": {"name": "Blur/22"}}, {"name": "Blur/24", "type": "number", "isAlias": true, "value": {"name": "Blur/24"}}, {"name": "Blur/26", "type": "number", "isAlias": true, "value": {"name": "Blur/26"}}, {"name": "Blur/28", "type": "number", "isAlias": true, "value": {"name": "Blur/28"}}, {"name": "Blur/30", "type": "number", "isAlias": true, "value": {"name": "Blur/30"}}, {"name": "Blur/32", "type": "number", "isAlias": true, "value": {"name": "Blur/32"}}]}]}, {"name": "<PERSON><PERSON>", "modes": [{"name": "Mode 1", "variables": [{"name": "elevations1/x-axis", "type": "number", "isAlias": true, "value": {"name": "X-Axis/none"}}, {"name": "elevations1/y-axis", "type": "number", "isAlias": true, "value": {"name": "Y-Axis/2"}}, {"name": "elevations1/blur", "type": "number", "isAlias": true, "value": {"name": "Blur/4"}}, {"name": "elevations1/spread", "type": "number", "isAlias": true, "value": {"collection": "Base Spacing", "name": "space1"}}, {"name": "elevations1/Color", "type": "color", "isAlias": true, "value": {"collection": "Base Color ", "name": "overlay-black/10"}}, {"name": "elevations2/x-axis", "type": "number", "isAlias": false, "value": 0}, {"name": "elevations2/y-axis", "type": "number", "isAlias": false, "value": 0}, {"name": "elevations2/blur", "type": "number", "isAlias": false, "value": 0}, {"name": "elevations2/spread", "type": "number", "isAlias": false, "value": 0}, {"name": "elevations2/color", "type": "number", "isAlias": false, "value": 0}, {"name": "elevations3/x-axis", "type": "number", "isAlias": false, "value": 0}, {"name": "elevations3/y-axis", "type": "number", "isAlias": false, "value": 0}, {"name": "elevations3/blur", "type": "number", "isAlias": false, "value": 0}, {"name": "elevations3/spread", "type": "number", "isAlias": false, "value": 0}, {"name": "elevations3/color", "type": "number", "isAlias": false, "value": 0}]}]}, {"name": "Typography", "modes": [{"name": "Style", "variables": [{"name": "M3/display/large", "type": "typography", "isAlias": false, "value": {"fontSize": 57, "fontFamily": "IBM Plex Sans Thai", "fontWeight": "Bold", "lineHeight": 88, "lineHeightUnit": "PIXELS", "letterSpacing": 0, "letterSpacingUnit": "PIXELS", "textCase": "ORIGINAL", "textDecoration": "NONE"}}, {"name": "M3/display/medium", "type": "typography", "isAlias": false, "value": {"fontSize": 45, "fontFamily": "IBM Plex Sans Thai", "fontWeight": "Bold", "lineHeight": 88, "lineHeightUnit": "PIXELS", "letterSpacing": 0, "letterSpacingUnit": "PIXELS", "textCase": "ORIGINAL", "textDecoration": "NONE"}}, {"name": "M3/display/small", "type": "typography", "isAlias": false, "value": {"fontSize": 36, "fontFamily": "IBM Plex Sans Thai", "fontWeight": "Bold", "lineHeight": 56, "lineHeightUnit": "PIXELS", "letterSpacing": 0, "letterSpacingUnit": "PIXELS", "textCase": "ORIGINAL", "textDecoration": "NONE"}}, {"name": "M3/headerLine/large", "type": "typography", "isAlias": false, "value": {"fontSize": 32, "fontFamily": "IBM Plex Sans Thai", "fontWeight": "Bold", "lineHeight": 52, "lineHeightUnit": "PIXELS", "letterSpacing": 0, "letterSpacingUnit": "PIXELS", "textCase": "ORIGINAL", "textDecoration": "NONE"}}, {"name": "M3/headerLine/medium", "type": "typography", "isAlias": false, "value": {"fontSize": 28, "fontFamily": "IBM Plex Sans Thai", "fontWeight": "Bold", "lineHeight": 80, "lineHeightUnit": "PIXELS", "letterSpacing": 0, "letterSpacingUnit": "PIXELS", "textCase": "ORIGINAL", "textDecoration": "NONE"}}, {"name": "M3/headerLine/small", "type": "typography", "isAlias": false, "value": {"fontSize": 24, "fontFamily": "IBM Plex Sans Thai", "fontWeight": "Bold", "lineHeight": 36, "lineHeightUnit": "PIXELS", "letterSpacing": 0, "letterSpacingUnit": "PIXELS", "textCase": "ORIGINAL", "textDecoration": "NONE"}}, {"name": "M3/title/large", "type": "typography", "isAlias": false, "value": {"fontSize": 22, "fontFamily": "IBM Plex Sans Thai", "fontWeight": "Medium", "lineHeight": 36, "lineHeightUnit": "PIXELS", "letterSpacing": 0, "letterSpacingUnit": "PIXELS", "textCase": "ORIGINAL", "textDecoration": "NONE"}}, {"name": "M3/title/medium", "type": "typography", "isAlias": false, "value": {"fontSize": 20, "fontFamily": "IBM Plex Sans Thai", "fontWeight": "Medium", "lineHeight": 36, "lineHeightUnit": "PIXELS", "letterSpacing": 0, "letterSpacingUnit": "PIXELS", "textCase": "ORIGINAL", "textDecoration": "NONE"}}, {"name": "M3/title/small", "type": "typography", "isAlias": false, "value": {"fontSize": 18, "fontFamily": "IBM Plex Sans Thai", "fontWeight": "Bold", "lineHeight": 30, "lineHeightUnit": "PIXELS", "letterSpacing": 0, "letterSpacingUnit": "PIXELS", "textCase": "ORIGINAL", "textDecoration": "NONE"}}, {"name": "M3/body/large", "type": "typography", "isAlias": false, "value": {"fontSize": 16, "fontFamily": "IBM Plex Sans Thai", "fontWeight": "Regular", "lineHeight": 24, "lineHeightUnit": "PIXELS", "letterSpacing": 0, "letterSpacingUnit": "PIXELS", "textCase": "ORIGINAL", "textDecoration": "NONE"}}, {"name": "M3/body/large-emphasized", "type": "typography", "isAlias": false, "value": {"fontSize": 16, "fontFamily": "IBM Plex Sans Thai", "fontWeight": "Medium", "lineHeight": 24, "lineHeightUnit": "PIXELS", "letterSpacing": 0, "letterSpacingUnit": "PIXELS", "textCase": "ORIGINAL", "textDecoration": "NONE"}}, {"name": "M3/body/medium", "type": "typography", "isAlias": false, "value": {"fontSize": 14, "fontFamily": "IBM Plex Sans Thai", "fontWeight": "Regular", "lineHeight": 24, "lineHeightUnit": "PIXELS", "letterSpacing": 0, "letterSpacingUnit": "PIXELS", "textCase": "ORIGINAL", "textDecoration": "NONE"}}, {"name": "M3/body/medium-emphasized", "type": "typography", "isAlias": false, "value": {"fontSize": 14, "fontFamily": "IBM Plex Sans Thai", "fontWeight": "Medium", "lineHeight": 24, "lineHeightUnit": "PIXELS", "letterSpacing": 0, "letterSpacingUnit": "PIXELS", "textCase": "ORIGINAL", "textDecoration": "NONE"}}, {"name": "M3/body/small", "type": "typography", "isAlias": false, "value": {"fontSize": 12, "fontFamily": "IBM Plex Sans Thai", "fontWeight": "Regular", "lineHeight": 20, "lineHeightUnit": "PIXELS", "letterSpacing": 0, "letterSpacingUnit": "PIXELS", "textCase": "ORIGINAL", "textDecoration": "NONE"}}, {"name": "M3/body/small-emphasized", "type": "typography", "isAlias": false, "value": {"fontSize": 12, "fontFamily": "IBM Plex Sans Thai", "fontWeight": "Medium", "lineHeight": 20, "lineHeightUnit": "PIXELS", "letterSpacing": 0, "letterSpacingUnit": "PIXELS", "textCase": "ORIGINAL", "textDecoration": "NONE"}}, {"name": "M3/label/large", "type": "typography", "isAlias": false, "value": {"fontSize": 14, "fontFamily": "IBM Plex Sans Thai", "fontWeight": "Regular", "lineHeight": 20, "lineHeightUnit": "PIXELS", "letterSpacing": 0, "letterSpacingUnit": "PIXELS", "textCase": "ORIGINAL", "textDecoration": "NONE"}}, {"name": "M3/label/large-emphasized", "type": "typography", "isAlias": false, "value": {"fontSize": 14, "fontFamily": "IBM Plex Sans Thai", "fontWeight": "Medium", "lineHeight": 20, "lineHeightUnit": "PIXELS", "letterSpacing": 0, "letterSpacingUnit": "PIXELS", "textCase": "ORIGINAL", "textDecoration": "NONE"}}, {"name": "M3/label/medium", "type": "typography", "isAlias": false, "value": {"fontSize": 12, "fontFamily": "IBM Plex Sans Thai", "fontWeight": "Regular", "lineHeight": 20, "lineHeightUnit": "PIXELS", "letterSpacing": 0, "letterSpacingUnit": "PIXELS", "textCase": "ORIGINAL", "textDecoration": "NONE"}}, {"name": "M3/label/medium-emphasized", "type": "typography", "isAlias": false, "value": {"fontSize": 12, "fontFamily": "IBM Plex Sans Thai", "fontWeight": "Medium", "lineHeight": 20, "lineHeightUnit": "PIXELS", "letterSpacing": 0, "letterSpacingUnit": "PIXELS", "textCase": "ORIGINAL", "textDecoration": "NONE"}}, {"name": "M3/label/small", "type": "typography", "isAlias": false, "value": {"fontSize": 10, "fontFamily": "IBM Plex Sans Thai", "fontWeight": "Regular", "lineHeight": 20, "lineHeightUnit": "PIXELS", "letterSpacing": 0, "letterSpacingUnit": "PIXELS", "textCase": "ORIGINAL", "textDecoration": "NONE"}}, {"name": "M3/label/small-emphasized", "type": "typography", "isAlias": false, "value": {"fontSize": 10, "fontFamily": "IBM Plex Sans Thai", "fontWeight": "Medium", "lineHeight": 20, "lineHeightUnit": "PIXELS", "letterSpacing": 0, "letterSpacingUnit": "PIXELS", "textCase": "ORIGINAL", "textDecoration": "NONE"}}]}]}, {"name": "Effects", "modes": [{"name": "Style", "variables": [{"name": "Toggle <PERSON>", "type": "effect", "isAlias": false, "value": {"effects": [{"type": "DROP_SHADOW", "color": {"r": 0, "g": 35, "b": 11, "a": 0.1}, "offset": {"x": 0, "y": 2}, "radius": 4, "spread": 0}]}}]}]}]}