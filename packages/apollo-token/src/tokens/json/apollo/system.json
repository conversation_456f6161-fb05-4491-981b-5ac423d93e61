{"alias": {"color": {"$description": "Alias tokens from Alias Color collection", "light": {"primary": {"primary": {"$type": "color", "$value": "{base.color.primary.40}"}, "surface-tint": {"$type": "color", "$value": "{base.color.primary.40}"}, "on-primary": {"$type": "color", "$value": "{base.color.primary.100}"}, "primary-container": {"$type": "color", "$value": "{base.color.primary.95}"}}, "error": {"error": {"$type": "color", "$value": "{base.color.danger.40}"}, "on-error": {"$type": "color", "$value": "{base.color.danger.100}"}, "error-container": {"$type": "color", "$value": "{base.color.danger.95}"}, "on-error-container": {"$type": "color", "$value": "{base.color.danger.30}"}}, "warning": {"warning": {"$type": "color", "$value": "{base.color.warning.50}"}, "on-warning": {"$type": "color", "$value": "{base.color.warning.100}"}, "warning-container": {"$type": "color", "$value": "{base.color.warning.95}"}, "on-warning-container": {"$type": "color", "$value": "{base.color.warning.30}"}}, "secondary": {"seconday": {"$type": "color", "$value": "{base.color.secondary.40}"}, "on-secondary": {"$type": "color", "$value": "{base.color.secondary.100}"}, "secondary-container": {"$type": "color", "$value": "{base.color.secondary.95}"}, "on-secondary-container": {"$type": "color", "$value": "{base.color.secondary.30}"}}, "tertiary": {"tertiary": {"$type": "color", "$value": "{base.color.tertiary.40}"}, "on-tertiary": {"$type": "color", "$value": "{base.color.tertiary.100}"}, "tertiary-container": {"$type": "color", "$value": "{base.color.tertiary.95}"}, "on-tertiary-container": {"$type": "color", "$value": "{base.color.tertiary.30}"}}, "background-and-surface": {"background": {"$type": "color", "$value": "{base.color.neutral.100}"}, "on-background": {"$type": "color", "$value": "{base.color.neutral.99}"}, "surface": {"$type": "color", "$value": "{base.color.neutral.99}"}, "on-surface": {"$type": "color", "$value": "{base.color.neutral.30}"}, "surface-variant": {"$type": "color", "$value": "{base.color.neutral.100}"}, "on-surface-variant": {"$type": "color", "$value": "{base.color.neutral.50}"}}, "outline-and-border": {"outline": {"$type": "color", "$value": "{base.color.neutral.70}"}, "outline-variant": {"$type": "color", "$value": "{base.color.neutral.80}"}, "border": {"$type": "color", "$value": "{base.color.neutral.30}"}}, "effects": {"shadow": {"$type": "color", "$value": "{base.color.overlay-black.40}"}, "scrim": {"$type": "color", "$value": "{base.color.overlay-black.40}"}}, "success": {"success": {"$type": "color", "$value": "{base.color.success.50}"}, "on-success": {"$type": "color", "$value": "{base.color.success.100}"}, "success-container": {"$type": "color", "$value": "{base.color.success.95}"}, "on-success-container": {"$type": "color", "$value": "{base.color.success.30}"}}}, "dark": {"primary": {"primary": {"$type": "color", "$value": "{base.color.primary.70}"}, "surface-tint": {"$type": "color", "$value": "{base.color.primary.70}"}, "on-primary": {"$type": "color", "$value": "{base.color.primary.20}"}, "primary-container": {"$type": "color", "$value": "{base.color.primary.30}"}}, "error": {"error": {"$type": "color", "$value": "{base.color.danger.90}"}, "on-error": {"$type": "color", "$value": "{base.color.danger.20}"}, "error-container": {"$type": "color", "$value": "{base.color.danger.30}"}, "on-error-container": {"$type": "color", "$value": "{base.color.danger.95}"}}, "warning": {"warning": {"$type": "color", "$value": "{base.color.warning.90}"}, "on-warning": {"$type": "color", "$value": "{base.color.warning.20}"}, "warning-container": {"$type": "color", "$value": "{base.color.warning.30}"}, "on-warning-container": {"$type": "color", "$value": "{base.color.danger.95}"}}, "secondary": {"seconday": {"$type": "color", "$value": "{base.color.secondary.70}"}, "on-secondary": {"$type": "color", "$value": "{base.color.secondary.20}"}, "secondary-container": {"$type": "color", "$value": "{base.color.secondary.30}"}, "on-secondary-container": {"$type": "color", "$value": "{base.color.secondary.95}"}}, "tertiary": {"tertiary": {"$type": "color", "$value": "{base.color.tertiary.70}"}, "on-tertiary": {"$type": "color", "$value": "{base.color.tertiary.20}"}, "tertiary-container": {"$type": "color", "$value": "{base.color.tertiary.30}"}, "on-tertiary-container": {"$type": "color", "$value": "{base.color.tertiary.95}"}}, "background-and-surface": {"background": {"$type": "color", "$value": "{base.color.neutral.10}"}, "on-background": {"$type": "color", "$value": "{base.color.neutral.20}"}, "surface": {"$type": "color", "$value": "{base.color.neutral.10}"}, "on-surface": {"$type": "color", "$value": "{base.color.neutral.99}"}, "surface-variant": {"$type": "color", "$value": "{base.color.neutral.70}"}, "on-surface-variant": {"$type": "color", "$value": "{base.color.neutral.95}"}}, "outline-and-border": {"outline": {"$type": "color", "$value": "{base.color.neutral.40}"}, "outline-variant": {"$type": "color", "$value": "{base.color.neutral.80}"}, "border": {"$type": "color", "$value": "{base.color.neutral.95}"}}, "effects": {"shadow": {"$type": "color", "$value": "{base.color.overlay-black.60}"}, "scrim": {"$type": "color", "$value": "{base.color.overlay-black.60}"}}, "success": {"success": {"$type": "color", "$value": "{base.color.primary.90}"}, "on-success": {"$type": "color", "$value": "{base.color.success.20}"}, "success-container": {"$type": "color", "$value": "{base.color.success.30}"}, "on-success-container": {"$type": "color", "$value": "{base.color.success.95}"}}}}, "typography": {"$description": "Alias tokens from Alias Typography collection", "font-family": {"$type": "string", "$value": "{base.typography.font-family.ibm-plex-sans-thai}"}, "display": {"large": {"font-family": {"$type": "string", "$value": "{base.typography.font-family.ibm-plex-sans-thai}"}, "font-weight": {"$type": "string", "$value": "{base.typography.font-weight.weight3}"}, "font-size": {"$type": "dimension", "$value": "{base.typography.font-size.3xl}"}, "line-height": {"$type": "dimension", "$value": "{base.typography.line-height.line-height49}"}}, "medium": {"font-family": {"$type": "string", "$value": "{base.typography.font-family.ibm-plex-sans-thai}"}, "font-weight": {"$type": "string", "$value": "{base.typography.font-weight.weight3}"}, "font-size": {"$type": "dimension", "$value": "{base.typography.font-size.2xl}"}, "line-height": {"$type": "dimension", "$value": "{base.typography.line-height.line-height49}"}}, "small": {"font-family": {"$type": "string", "$value": "{base.typography.font-family.ibm-plex-sans-thai}"}, "font-weight": {"$type": "string", "$value": "{base.typography.font-weight.weight3}"}, "font-size": {"$type": "dimension", "$value": "{base.typography.font-size.xl}"}, "line-height": {"$type": "dimension", "$value": "{base.typography.line-height.line-height31}"}}}, "headline": {"large": {"font-family": {"$type": "string", "$value": "{base.typography.font-family.ibm-plex-sans-thai}"}, "font-weight": {"$type": "string", "$value": "{base.typography.font-weight.weight3}"}, "font-size": {"$type": "dimension", "$value": "{base.typography.font-size.lg}"}, "line-height": {"$type": "dimension", "$value": "{base.typography.line-height.line-height29}"}}, "medium": {"font-family": {"$type": "string", "$value": "{base.typography.font-family.ibm-plex-sans-thai}"}, "font-weight": {"$type": "string", "$value": "{base.typography.font-weight.weight3}"}, "font-size": {"$type": "dimension", "$value": "{base.typography.font-size.md}"}, "line-height": {"$type": "dimension", "$value": "{base.typography.line-height.line-height44}"}}, "small": {"font-family": {"$type": "string", "$value": "{base.typography.font-family.ibm-plex-sans-thai}"}, "font-weight": {"$type": "string", "$value": "{base.typography.font-weight.weight3}"}, "font-size": {"$type": "dimension", "$value": "{base.typography.font-size.sm}"}, "line-height": {"$type": "dimension", "$value": "{base.typography.line-height.line-height21}"}}}, "title": {"large": {"font-family": {"$type": "string", "$value": "{base.typography.font-family.ibm-plex-sans-thai}"}, "font-weight": {"$type": "string", "$value": "{base.typography.font-weight.weight2}"}, "font-size": {"$type": "dimension", "$value": "{base.typography.font-size.xs-plus}"}, "line-height": {"$type": "dimension", "$value": "{base.typography.line-height.line-height21}"}}, "medium": {"font-family": {"$type": "string", "$value": "{base.typography.font-family.ibm-plex-sans-thai}"}, "font-weight": {"$type": "string", "$value": "{base.typography.font-weight.weight2}"}, "font-size": {"$type": "dimension", "$value": "{base.typography.font-size.xs-md}"}, "line-height": {"$type": "dimension", "$value": "{base.typography.line-height.line-height21}"}}, "small": {"font-family": {"$type": "string", "$value": "{base.typography.font-family.ibm-plex-sans-thai}"}, "font-weight": {"$type": "string", "$value": "{base.typography.font-weight.weight3}"}, "font-size": {"$type": "dimension", "$value": "{base.typography.font-size.xs-sm}"}, "line-height": {"$type": "dimension", "$value": "{base.typography.line-height.line-height17}"}}}, "body": {"large": {"font-family": {"$type": "string", "$value": "{base.typography.font-family.ibm-plex-sans-thai}"}, "font-weight": {"$type": "string", "$value": "{base.typography.font-weight.weight1}"}, "weight-emphasized": {"$type": "string", "$value": "{base.typography.font-weight.weight2}"}, "font-size": {"$type": "dimension", "$value": "{base.typography.font-size.xs}"}, "line-height": {"$type": "dimension", "$value": "{base.typography.line-height.line-height13}"}}, "medium": {"font-family": {"$type": "string", "$value": "{base.typography.font-family.ibm-plex-sans-thai}"}, "font-weight": {"$type": "string", "$value": "{base.typography.font-weight.weight1}"}, "weight-emphasized": {"$type": "string", "$value": "{base.typography.font-weight.weight2}"}, "font-size": {"$type": "dimension", "$value": "{base.typography.font-size.2xs}"}, "line-height": {"$type": "dimension", "$value": "{base.typography.line-height.line-height13}"}}, "small": {"font-family": {"$type": "string", "$value": "{base.typography.font-family.ibm-plex-sans-thai}"}, "font-weight": {"$type": "string", "$value": "{base.typography.font-weight.weight1}"}, "weight-emphasized": {"$type": "string", "$value": "{base.typography.font-weight.weight2}"}, "font-size": {"$type": "dimension", "$value": "{base.typography.font-size.3xs}"}, "line-height": {"$type": "dimension", "$value": "{base.typography.line-height.line-height10}"}}}, "label": {"large": {"font-family": {"$type": "string", "$value": "{base.typography.font-family.ibm-plex-sans-thai}"}, "font-weight": {"$type": "string", "$value": "{base.typography.font-weight.weight1}"}, "weight-emphasized": {"$type": "string", "$value": "{base.typography.font-weight.weight2}"}, "font-size": {"$type": "dimension", "$value": "{base.typography.font-size.2xs}"}, "line-height": {"$type": "dimension", "$value": "{base.typography.line-height.line-height10}"}}, "medium": {"font-family": {"$type": "string", "$value": "{base.typography.font-family.ibm-plex-sans-thai}"}, "font-weight": {"$type": "string", "$value": "{base.typography.font-weight.weight1}"}, "weight-emphasized": {"$type": "string", "$value": "{base.typography.font-weight.weight2}"}, "font-size": {"$type": "dimension", "$value": "{base.typography.font-size.3xs}"}, "line-height": {"$type": "dimension", "$value": "{base.typography.line-height.line-height10}"}}, "small": {"font-family": {"$type": "string", "$value": "{base.typography.font-family.ibm-plex-sans-thai}"}, "font-weight": {"$type": "string", "$value": "{base.typography.font-weight.weight1}"}, "weight-emphasized": {"$type": "string", "$value": "{base.typography.font-weight.weight2}"}, "font-size": {"$type": "dimension", "$value": "{base.typography.font-size.4xs}"}, "line-height": {"$type": "dimension", "$value": "{base.typography.line-height.line-height13}"}}}}, "spacing": {"$description": "Alias tokens from Alias Spacing collection", "margin": {"vertical": {"vertical": {"$type": "dimension", "$value": "{base.spacing.space15}"}}, "horizontal": {"horizontal": {"$type": "dimension", "$value": "{base.spacing.space15}"}}}, "padding": {"padding1": {"$type": "dimension", "$value": "{base.spacing.space1}"}, "padding2": {"$type": "dimension", "$value": "{base.spacing.space2}"}, "padding3": {"$type": "dimension", "$value": "{base.spacing.space3}"}, "padding4": {"$type": "dimension", "$value": "{base.spacing.space4}"}, "padding5": {"$type": "dimension", "$value": "{base.spacing.space5}"}, "padding6": {"$type": "dimension", "$value": "{base.spacing.space6}"}, "padding7": {"$type": "dimension", "$value": "{base.spacing.space7}"}, "padding8": {"$type": "dimension", "$value": "{base.spacing.space8}"}, "padding9": {"$type": "dimension", "$value": "{base.spacing.space9}"}, "padding10": {"$type": "dimension", "$value": "{base.spacing.space10}"}, "padding11": {"$type": "dimension", "$value": "{base.spacing.space11}"}, "padding12": {"$type": "dimension", "$value": "{base.spacing.space12}"}}, "gap": {"gap1": {"$type": "dimension", "$value": "{base.spacing.space1}"}, "gap2": {"$type": "dimension", "$value": "{base.spacing.space2}"}, "gap3": {"$type": "dimension", "$value": "{base.spacing.space3}"}, "gap4": {"$type": "dimension", "$value": "{base.spacing.space4}"}, "gap5": {"$type": "dimension", "$value": "{base.spacing.space5}"}, "gap6": {"$type": "dimension", "$value": "{base.spacing.space6}"}, "gap7": {"$type": "dimension", "$value": "{base.spacing.space7}"}, "gap8": {"$type": "dimension", "$value": "{base.spacing.space8}"}, "gap9": {"$type": "dimension", "$value": "{base.spacing.space9}"}, "gap10": {"$type": "dimension", "$value": "{base.spacing.space10}"}, "gap11": {"$type": "dimension", "$value": "{base.spacing.space11}"}, "gap12": {"$type": "dimension", "$value": "{base.spacing.space12}"}}}, "radius": {"$description": "Alias tokens from <PERSON>as Radius collection", "radius1": {"$type": "dimension", "$value": "{base.radius.none}"}, "radius2": {"$type": "dimension", "$value": "{base.radius.4}"}, "radius3": {"$type": "dimension", "$value": "{base.radius.6}"}, "radius4": {"$type": "dimension", "$value": "{base.radius.8}"}, "radius5": {"$type": "dimension", "$value": "{base.radius.10}"}, "radius6": {"$type": "dimension", "$value": "{base.radius.12}"}, "radius7": {"$type": "dimension", "$value": "{base.radius.14}"}, "radius8": {"$type": "dimension", "$value": "{base.radius.16}"}, "radius9": {"$type": "dimension", "$value": "{base.radius.20}"}, "radius10": {"$type": "dimension", "$value": "{base.radius.24}"}, "radius11": {"$type": "dimension", "$value": "{base.radius.full}"}}, "elevation": {"$description": "Alias tokens from Alias Elevations collection", "elevations1": {"x-axis": {"$type": "dimension", "$value": "{base.elevation.x-axis.none}"}, "y-axis": {"$type": "dimension", "$value": "{base.elevation.y-axis.2}"}, "blur": {"$type": "dimension", "$value": "{base.elevation.blur.4}"}, "spread": {"$type": "dimension", "$value": "{base.elevation.space1}"}, "color": {"$type": "color", "$value": "{base.elevation.overlay-black.10}"}}, "elevations2": {"x-axis": {"$type": "dimension", "$value": "0px"}, "y-axis": {"$type": "dimension", "$value": "0px"}, "blur": {"$type": "dimension", "$value": "0px"}, "spread": {"$type": "dimension", "$value": "0px"}, "color": {"$type": "dimension", "$value": "0px"}}, "elevations3": {"x-axis": {"$type": "dimension", "$value": "0px"}, "y-axis": {"$type": "dimension", "$value": "0px"}, "blur": {"$type": "dimension", "$value": "0px"}, "spread": {"$type": "dimension", "$value": "0px"}, "color": {"$type": "dimension", "$value": "0px"}}}}, "base": {"color": {"$description": "Base tokens from Base Color  collection", "primary": {"0": {"$type": "color", "$value": "{color.green-pine.0}"}, "10": {"$type": "color", "$value": "{color.green-pine.10}"}, "20": {"$type": "color", "$value": "{color.green-pine.20}"}, "30": {"$type": "color", "$value": "{color.green-pine.30}"}, "40": {"$type": "color", "$value": "{color.green-pine.40}"}, "50": {"$type": "color", "$value": "{color.green-pine.50}"}, "60": {"$type": "color", "$value": "{color.green-pine.60}"}, "70": {"$type": "color", "$value": "{color.green-pine.70}"}, "80": {"$type": "color", "$value": "{color.green-pine.80}"}, "90": {"$type": "color", "$value": "{color.green-pine.90}"}, "95": {"$type": "color", "$value": "{color.green-pine.95}"}, "99": {"$type": "color", "$value": "{color.green-pine.99}"}, "100": {"$type": "color", "$value": "{color.green-pine.100}"}}, "secondary": {"0": {"$type": "color", "$value": "{color.gray-bluish.0}"}, "10": {"$type": "color", "$value": "{color.gray-bluish.10}"}, "20": {"$type": "color", "$value": "{color.gray-bluish.20}"}, "30": {"$type": "color", "$value": "{color.gray-bluish.30}"}, "40": {"$type": "color", "$value": "{color.gray-bluish.40}"}, "50": {"$type": "color", "$value": "{color.gray-bluish.50}"}, "60": {"$type": "color", "$value": "{color.gray-bluish.60}"}, "70": {"$type": "color", "$value": "{color.gray-bluish.70}"}, "80": {"$type": "color", "$value": "{color.gray-bluish.80}"}, "90": {"$type": "color", "$value": "{color.gray-bluish.90}"}, "95": {"$type": "color", "$value": "{color.gray-bluish.95}"}, "99": {"$type": "color", "$value": "{color.gray-bluish.99}"}, "100": {"$type": "color", "$value": "{color.gray-bluish.100}"}}, "tertiary": {"0": {"$type": "color", "$value": "{color.blue-ocean.0}"}, "10": {"$type": "color", "$value": "{color.blue-ocean.10}"}, "20": {"$type": "color", "$value": "{color.blue-ocean.20}"}, "30": {"$type": "color", "$value": "{color.blue-ocean.30}"}, "40": {"$type": "color", "$value": "{color.blue-ocean.40}"}, "50": {"$type": "color", "$value": "{color.blue-ocean.50}"}, "60": {"$type": "color", "$value": "{color.blue-ocean.60}"}, "70": {"$type": "color", "$value": "{color.blue-ocean.70}"}, "80": {"$type": "color", "$value": "{color.blue-ocean.80}"}, "90": {"$type": "color", "$value": "{color.blue-ocean.90}"}, "95": {"$type": "color", "$value": "{color.blue-ocean.95}"}, "99": {"$type": "color", "$value": "{color.blue-ocean.99}"}, "100": {"$type": "color", "$value": "{color.blue-ocean.100}"}}, "neutral": {"0": {"$type": "color", "$value": "{color.gray-smoke.0}"}, "10": {"$type": "color", "$value": "{color.gray-smoke.10}"}, "20": {"$type": "color", "$value": "{color.gray-smoke.20}"}, "30": {"$type": "color", "$value": "{color.gray-smoke.30}"}, "40": {"$type": "color", "$value": "{color.gray-smoke.40}"}, "50": {"$type": "color", "$value": "{color.gray-smoke.50}"}, "60": {"$type": "color", "$value": "{color.gray-smoke.60}"}, "70": {"$type": "color", "$value": "{color.gray-smoke.70}"}, "80": {"$type": "color", "$value": "{color.gray-smoke.80}"}, "90": {"$type": "color", "$value": "{color.gray-smoke.90}"}, "95": {"$type": "color", "$value": "{color.gray-smoke.95}"}, "99": {"$type": "color", "$value": "{color.gray-smoke.99}"}, "100": {"$type": "color", "$value": "{color.gray-smoke.100}"}}, "danger": {"0": {"$type": "color", "$value": "{color.red-cherry.0}"}, "10": {"$type": "color", "$value": "{color.red-cherry.10}"}, "20": {"$type": "color", "$value": "{color.red-cherry.20}"}, "30": {"$type": "color", "$value": "{color.red-cherry.30}"}, "40": {"$type": "color", "$value": "{color.red-cherry.40}"}, "50": {"$type": "color", "$value": "{color.red-cherry.50}"}, "60": {"$type": "color", "$value": "{color.red-cherry.60}"}, "70": {"$type": "color", "$value": "{color.red-cherry.70}"}, "80": {"$type": "color", "$value": "{color.red-cherry.80}"}, "90": {"$type": "color", "$value": "{color.red-cherry.90}"}, "95": {"$type": "color", "$value": "{color.red-cherry.95}"}, "99": {"$type": "color", "$value": "{color.red-cherry.99}"}, "100": {"$type": "color", "$value": "{color.red-cherry.100}"}}, "warning": {"0": {"$type": "color", "$value": "{color.yellow-peanut.0}"}, "10": {"$type": "color", "$value": "{color.yellow-peanut.10}"}, "20": {"$type": "color", "$value": "{color.yellow-peanut.20}"}, "30": {"$type": "color", "$value": "{color.yellow-peanut.30}"}, "40": {"$type": "color", "$value": "{color.yellow-peanut.40}"}, "50": {"$type": "color", "$value": "{color.yellow-peanut.50}"}, "60": {"$type": "color", "$value": "{color.yellow-peanut.60}"}, "70": {"$type": "color", "$value": "{color.yellow-peanut.70}"}, "80": {"$type": "color", "$value": "{color.yellow-peanut.80}"}, "90": {"$type": "color", "$value": "{color.yellow-peanut.90}"}, "95": {"$type": "color", "$value": "{color.yellow-peanut.95}"}, "99": {"$type": "color", "$value": "{color.yellow-peanut.99}"}, "100": {"$type": "color", "$value": "{color.yellow-peanut.100}"}}, "success": {"0": {"$type": "color", "$value": "{color.green-matcha.0}"}, "10": {"$type": "color", "$value": "{color.green-matcha.10}"}, "20": {"$type": "color", "$value": "{color.green-matcha.20}"}, "30": {"$type": "color", "$value": "{color.green-matcha.30}"}, "40": {"$type": "color", "$value": "{color.green-matcha.40}"}, "50": {"$type": "color", "$value": "{color.green-matcha.50}"}, "60": {"$type": "color", "$value": "{color.green-matcha.60}"}, "70": {"$type": "color", "$value": "{color.green-matcha.70}"}, "80": {"$type": "color", "$value": "{color.green-matcha.80}"}, "90": {"$type": "color", "$value": "{color.green-matcha.90}"}, "95": {"$type": "color", "$value": "{color.green-matcha.95}"}, "99": {"$type": "color", "$value": "{color.green-matcha.99}"}, "100": {"$type": "color", "$value": "{color.green-matcha.100}"}}, "overlay-black": {"0": {"$type": "color", "$value": "{color.black-soft.0}"}, "10": {"$type": "color", "$value": "{color.black-soft.10}"}, "20": {"$type": "color", "$value": "{color.black-soft.20}"}, "30": {"$type": "color", "$value": "{color.black-soft.30}"}, "40": {"$type": "color", "$value": "{color.black-soft.40}"}, "50": {"$type": "color", "$value": "{color.black-soft.50}"}, "60": {"$type": "color", "$value": "{color.black-soft.60}"}, "70": {"$type": "color", "$value": "{color.black-soft.70}"}, "80": {"$type": "color", "$value": "{color.black-soft.80}"}, "90": {"$type": "color", "$value": "{color.black-soft.90}"}, "100": {"$type": "color", "$value": "{color.black-soft.100}"}}, "overlay-white": {"0": {"$type": "color", "$value": "{color.white-soft.0}"}, "10": {"$type": "color", "$value": "{color.white-soft.10}"}, "20": {"$type": "color", "$value": "{color.white-soft.20}"}, "30": {"$type": "color", "$value": "{color.white-soft.30}"}, "40": {"$type": "color", "$value": "{color.white-soft.40}"}, "50": {"$type": "color", "$value": "{color.white-soft.50}"}, "60": {"$type": "color", "$value": "{color.white-soft.60}"}, "70": {"$type": "color", "$value": "{color.white-soft.70}"}, "80": {"$type": "color", "$value": "{color.white-soft.80}"}, "90": {"$type": "color", "$value": "{color.white-soft.90}"}, "100": {"$type": "color", "$value": "{color.white-soft.100}"}}, "kid-club": {"0": {"$type": "color", "$value": "{color.lilac-soft.0}"}, "color": {"$type": "color", "$value": "#FFFFFF"}, "color-2": {"$type": "color", "$value": "#FFFFFF"}, "color-3": {"$type": "color", "$value": "#FFFFFF"}, "color-4": {"$type": "color", "$value": "#FFFFFF"}, "color-5": {"$type": "color", "$value": "#FFFFFF"}, "color-6": {"$type": "color", "$value": "#FFFFFF"}, "color-7": {"$type": "color", "$value": "#FFFFFF"}, "color-8": {"$type": "color", "$value": "#FFFFFF"}, "color-9": {"$type": "color", "$value": "#FFFFFF"}, "color-10": {"$type": "color", "$value": "#FFFFFF"}, "color-11": {"$type": "color", "$value": "#FFFFFF"}, "color-12": {"$type": "color", "$value": "#FFFFFF"}}}, "spacing": {"$description": "Base tokens from Base Spacing collection", "space1": {"$type": "dimension", "$value": "{spacing.none}"}, "space2": {"$type": "dimension", "$value": "{spacing.2}"}, "space3": {"$type": "dimension", "$value": "{spacing.4}"}, "space4": {"$type": "dimension", "$value": "{spacing.6}"}, "space5": {"$type": "dimension", "$value": "{spacing.8}"}, "space6": {"$type": "dimension", "$value": "{spacing.10}"}, "space7": {"$type": "dimension", "$value": "{spacing.12}"}, "space8": {"$type": "dimension", "$value": "{spacing.16}"}, "space9": {"$type": "dimension", "$value": "{spacing.20}"}, "space10": {"$type": "dimension", "$value": "{spacing.24}"}, "space11": {"$type": "dimension", "$value": "{spacing.28}"}, "space12": {"$type": "dimension", "$value": "{spacing.32}"}, "space13": {"$type": "dimension", "$value": "{spacing.36}"}, "space14": {"$type": "dimension", "$value": "{spacing.40}"}, "space15": {"$type": "dimension", "$value": "{spacing.48}"}, "neg-space1": {"$type": "dimension", "$value": "{spacing.2}"}, "neg-space2": {"$type": "dimension", "$value": "{spacing.4}"}, "neg-space3": {"$type": "dimension", "$value": "{spacing.6}"}, "neg-space4": {"$type": "dimension", "$value": "{spacing.8}"}, "tertiary": {"tertiary": {"$type": "color", "$value": "{spacing.tertiary.40}"}}, "title": {"large": {"font-size": {"$type": "dimension", "$value": "{spacing.font-scale.scale8}"}, "line-height": {"$type": "dimension", "$value": "{spacing.line-height.height8}"}, "letter-spacing": {"$type": "dimension", "$value": "{spacing.letter-spacing.letter3}"}}}}, "typography": {"$description": "Base tokens from Base Typography collection", "font-family": {"$type": "string", "$value": "{typography.font-family.ibm-plex-sans-thai}"}, "font-size": {"3xl": {"$type": "dimension", "$value": "{typography.font-size.57}"}, "2xl": {"$type": "dimension", "$value": "{typography.font-size.45}"}, "xl": {"$type": "dimension", "$value": "{typography.font-size.36}"}, "lg": {"$type": "dimension", "$value": "{typography.font-size.32}"}, "md": {"$type": "dimension", "$value": "{typography.font-size.28}"}, "sm": {"$type": "dimension", "$value": "{typography.font-size.24}"}, "xs-plus": {"$type": "dimension", "$value": "{typography.font-size.22}"}, "xs-md": {"$type": "dimension", "$value": "{typography.font-size.20}"}, "xs-sm": {"$type": "dimension", "$value": "{typography.font-size.18}"}, "xs": {"$type": "dimension", "$value": "{typography.font-size.16}"}, "2xs": {"$type": "dimension", "$value": "{typography.font-size.14}"}, "3xs": {"$type": "dimension", "$value": "{typography.font-size.12}"}, "4xs": {"$type": "dimension", "$value": "{typography.font-size.10}"}}, "font-weight": {"weight1": {"$type": "string", "$value": "{typography.font-weight.regular}"}, "weight2": {"$type": "string", "$value": "{typography.font-weight.medium}"}, "weight3": {"$type": "string", "$value": "{typography.font-weight.bold}"}}, "line-height": {"line-height1": {"$type": "dimension", "$value": "{typography.line-height.4}"}, "line-height2": {"$type": "dimension", "$value": "{typography.line-height.6}"}, "line-height3": {"$type": "dimension", "$value": "{typography.line-height.8}"}, "line-height4": {"$type": "dimension", "$value": "{typography.line-height.10}"}, "line-height5": {"$type": "dimension", "$value": "{typography.line-height.12}"}, "line-height6": {"$type": "dimension", "$value": "{typography.line-height.14}"}, "line-height7": {"$type": "dimension", "$value": "{typography.line-height.15}"}, "line-height8": {"$type": "dimension", "$value": "{typography.line-height.16}"}, "line-height9": {"$type": "dimension", "$value": "{typography.line-height.18}"}, "line-height10": {"$type": "dimension", "$value": "{typography.line-height.20}"}, "line-height11": {"$type": "dimension", "$value": "{typography.line-height.21}"}, "line-height12": {"$type": "dimension", "$value": "{typography.line-height.22}"}, "line-height13": {"$type": "dimension", "$value": "{typography.line-height.24}"}, "line-height14": {"$type": "dimension", "$value": "{typography.line-height.26}"}, "line-height15": {"$type": "dimension", "$value": "{typography.line-height.27}"}, "line-height16": {"$type": "dimension", "$value": "{typography.line-height.28}"}, "line-height17": {"$type": "dimension", "$value": "{typography.line-height.30}"}, "line-height18": {"$type": "dimension", "$value": "{typography.line-height.32}"}, "line-height19": {"$type": "dimension", "$value": "{typography.line-height.33}"}, "line-height20": {"$type": "dimension", "$value": "{typography.line-height.34}"}, "line-height21": {"$type": "dimension", "$value": "{typography.line-height.36}"}, "line-height22": {"$type": "dimension", "$value": "{typography.line-height.38}"}, "line-height23": {"$type": "dimension", "$value": "{typography.line-height.40}"}, "line-height24": {"$type": "dimension", "$value": "{typography.line-height.42}"}, "line-height25": {"$type": "dimension", "$value": "{typography.line-height.44}"}, "line-height26": {"$type": "dimension", "$value": "{typography.line-height.46}"}, "line-height27": {"$type": "dimension", "$value": "{typography.line-height.48}"}, "line-height28": {"$type": "dimension", "$value": "{typography.line-height.50}"}, "line-height29": {"$type": "dimension", "$value": "{typography.line-height.52}"}, "line-height30": {"$type": "dimension", "$value": "{typography.line-height.54}"}, "line-height31": {"$type": "dimension", "$value": "{typography.line-height.56}"}, "line-height32": {"$type": "dimension", "$value": "{typography.line-height.58}"}, "line-height33": {"$type": "dimension", "$value": "{typography.line-height.60}"}, "line-height34": {"$type": "dimension", "$value": "{typography.line-height.62}"}, "line-height35": {"$type": "dimension", "$value": "{typography.line-height.64}"}, "line-height36": {"$type": "dimension", "$value": "{typography.line-height.66}"}, "line-height37": {"$type": "dimension", "$value": "{typography.line-height.67}"}, "line-height38": {"$type": "dimension", "$value": "{typography.line-height.68}"}, "line-height39": {"$type": "dimension", "$value": "{typography.line-height.70}"}, "line-height40": {"$type": "dimension", "$value": "{typography.line-height.72}"}, "line-height41": {"$type": "dimension", "$value": "{typography.line-height.74}"}, "line-height42": {"$type": "dimension", "$value": "{typography.line-height.76}"}, "line-height43": {"$type": "dimension", "$value": "{typography.line-height.78}"}, "line-height44": {"$type": "dimension", "$value": "{typography.line-height.80}"}, "line-height45": {"$type": "dimension", "$value": "{typography.line-height.82}"}, "line-height46": {"$type": "dimension", "$value": "{typography.line-height.84}"}, "line-height47": {"$type": "dimension", "$value": "{typography.line-height.85}"}, "line-height48": {"$type": "dimension", "$value": "{typography.line-height.86}"}, "line-height49": {"$type": "dimension", "$value": "{typography.line-height.88}"}, "line-height50": {"$type": "dimension", "$value": "{typography.line-height.96}"}, "line-height51": {"$type": "dimension", "$value": "{typography.line-height.132}"}}}, "radius": {"2": {"$type": "dimension", "$value": "{radius.2}"}, "4": {"$type": "dimension", "$value": "{radius.4}"}, "6": {"$type": "dimension", "$value": "{radius.6}"}, "8": {"$type": "dimension", "$value": "{radius.8}"}, "10": {"$type": "dimension", "$value": "{radius.10}"}, "12": {"$type": "dimension", "$value": "{radius.12}"}, "14": {"$type": "dimension", "$value": "{radius.14}"}, "16": {"$type": "dimension", "$value": "{radius.16}"}, "18": {"$type": "dimension", "$value": "{radius.18}"}, "20": {"$type": "dimension", "$value": "{radius.20}"}, "22": {"$type": "dimension", "$value": "{radius.22}"}, "24": {"$type": "dimension", "$value": "{radius.24}"}, "26": {"$type": "dimension", "$value": "{radius.26}"}, "28": {"$type": "dimension", "$value": "{radius.28}"}, "30": {"$type": "dimension", "$value": "{radius.30}"}, "32": {"$type": "dimension", "$value": "{radius.32}"}, "34": {"$type": "dimension", "$value": "{radius.34}"}, "36": {"$type": "dimension", "$value": "{radius.36}"}, "38": {"$type": "dimension", "$value": "{radius.38}"}, "40": {"$type": "dimension", "$value": "{radius.40}"}, "42": {"$type": "dimension", "$value": "{radius.42}"}, "44": {"$type": "dimension", "$value": "{radius.44}"}, "46": {"$type": "dimension", "$value": "{radius.46}"}, "48": {"$type": "dimension", "$value": "{radius.48}"}, "50": {"$type": "dimension", "$value": "{radius.50}"}, "52": {"$type": "dimension", "$value": "{radius.52}"}, "54": {"$type": "dimension", "$value": "{radius.54}"}, "56": {"$type": "dimension", "$value": "{radius.56}"}, "58": {"$type": "dimension", "$value": "{radius.58}"}, "60": {"$type": "dimension", "$value": "{radius.60}"}, "62": {"$type": "dimension", "$value": "{radius.62}"}, "64": {"$type": "dimension", "$value": "{radius.64}"}, "66": {"$type": "dimension", "$value": "{radius.66}"}, "68": {"$type": "dimension", "$value": "{radius.68}"}, "70": {"$type": "dimension", "$value": "{radius.70}"}, "72": {"$type": "dimension", "$value": "{radius.72}"}, "74": {"$type": "dimension", "$value": "{radius.74}"}, "76": {"$type": "dimension", "$value": "{radius.76}"}, "78": {"$type": "dimension", "$value": "{radius.78}"}, "80": {"$type": "dimension", "$value": "{radius.80}"}, "$description": "Base tokens from Base Radius collection", "none": {"$type": "dimension", "$value": "{radius.none}"}, "full": {"$type": "dimension", "$value": "{radius.full}"}}, "elevation": {"$description": "Base tokens from Base Elevations collection", "x-axis": {"1": {"$type": "dimension", "$value": "{elevation.x-axis.1}"}, "2": {"$type": "dimension", "$value": "{elevation.x-axis.2}"}, "4": {"$type": "dimension", "$value": "{elevation.x-axis.4}"}, "6": {"$type": "dimension", "$value": "{elevation.x-axis.6}"}, "8": {"$type": "dimension", "$value": "{elevation.x-axis.8}"}, "10": {"$type": "dimension", "$value": "{elevation.x-axis.10}"}, "12": {"$type": "dimension", "$value": "{elevation.x-axis.12}"}, "14": {"$type": "dimension", "$value": "{elevation.x-axis.14}"}, "16": {"$type": "dimension", "$value": "{elevation.x-axis.16}"}, "18": {"$type": "dimension", "$value": "{elevation.x-axis.18}"}, "20": {"$type": "dimension", "$value": "{elevation.x-axis.20}"}, "22": {"$type": "dimension", "$value": "{elevation.y-axis.22}"}, "24": {"$type": "dimension", "$value": "{elevation.x-axis.24}"}, "none": {"$type": "dimension", "$value": "{elevation.x-axis.none}"}}, "y-axis": {"1": {"$type": "dimension", "$value": "{elevation.y-axis.1}"}, "2": {"$type": "dimension", "$value": "{elevation.y-axis.2}"}, "4": {"$type": "dimension", "$value": "{elevation.y-axis.4}"}, "6": {"$type": "dimension", "$value": "{elevation.y-axis.6}"}, "8": {"$type": "dimension", "$value": "{elevation.y-axis.8}"}, "10": {"$type": "dimension", "$value": "{elevation.y-axis.10}"}, "12": {"$type": "dimension", "$value": "{elevation.y-axis.12}"}, "14": {"$type": "dimension", "$value": "{elevation.y-axis.14}"}, "16": {"$type": "dimension", "$value": "{elevation.y-axis.16}"}, "18": {"$type": "dimension", "$value": "{elevation.y-axis.18}"}, "20": {"$type": "dimension", "$value": "{elevation.y-axis.20}"}, "22": {"$type": "dimension", "$value": "{elevation.y-axis.22}"}, "24": {"$type": "dimension", "$value": "{elevation.y-axis.24}"}, "none": {"$type": "dimension", "$value": "{elevation.y-axis.none}"}}, "spread": {"1": {"$type": "dimension", "$value": "{elevation.spread.1}"}, "2": {"$type": "dimension", "$value": "{elevation.spread.2}"}, "4": {"$type": "dimension", "$value": "{elevation.spread.4}"}, "6": {"$type": "dimension", "$value": "{elevation.spread.8}"}, "8": {"$type": "dimension", "$value": "{elevation.spread.8}"}, "10": {"$type": "dimension", "$value": "{elevation.spread.10}"}, "12": {"$type": "dimension", "$value": "{elevation.spread.12}"}, "14": {"$type": "dimension", "$value": "{elevation.spread.14}"}, "16": {"$type": "dimension", "$value": "{elevation.spread.16}"}, "18": {"$type": "dimension", "$value": "{elevation.spread.18}"}, "20": {"$type": "dimension", "$value": "{elevation.spread.20}"}, "22": {"$type": "dimension", "$value": "{elevation.spread.22}"}, "24": {"$type": "dimension", "$value": "{elevation.spread.24}"}, "none": {"$type": "dimension", "$value": "{elevation.spread.none}"}}, "blur": {"2": {"$type": "dimension", "$value": "{elevation.blur.2}"}, "4": {"$type": "dimension", "$value": "{elevation.blur.4}"}, "6": {"$type": "dimension", "$value": "{elevation.blur.6}"}, "8": {"$type": "dimension", "$value": "{elevation.blur.8}"}, "10": {"$type": "dimension", "$value": "{elevation.blur.10}"}, "12": {"$type": "dimension", "$value": "{elevation.blur.12}"}, "14": {"$type": "dimension", "$value": "{elevation.blur.14}"}, "16": {"$type": "dimension", "$value": "{elevation.blur.16}"}, "18": {"$type": "dimension", "$value": "{elevation.blur.18}"}, "20": {"$type": "dimension", "$value": "{elevation.blur.20}"}, "22": {"$type": "dimension", "$value": "{elevation.blur.22}"}, "24": {"$type": "dimension", "$value": "{elevation.blur.24}"}, "26": {"$type": "dimension", "$value": "{elevation.blur.26}"}, "28": {"$type": "dimension", "$value": "{elevation.blur.28}"}, "30": {"$type": "dimension", "$value": "{elevation.blur.30}"}, "32": {"$type": "dimension", "$value": "{elevation.blur.32}"}, "none": {"$type": "dimension", "$value": "{elevation.blur.none}"}}}, "effects": {"$description": "Base tokens from Effects collection"}}}