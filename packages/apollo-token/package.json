{"name": "@apollo/token", "version": "0.0.1-alpha.1", "description": "a design tokens with custom transformer to support tailwind format", "type": "commonjs", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.umd.js", "types": "./dist/index.d.ts"}, "./tailwind": "./dist/index.mjs", "./style.css": "./dist/index.css"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "pnpm run build:raw && pnpm run extract:raw && pnpm run build:sd && node scripts/generate-files.js && pnpm run build:final", "build:raw": "pnpm token-transformer src/raw-tokens src/tokens/core-resolved.json", "extract:raw": "pnpm node-jq '.global * .Alias' src/tokens/core-resolved.json > src/tokens/core.json", "build:sd": "style-dictionary build --config src/sd.config.js", "build:sdtw": "node src/sd-tw.config.js", "build:final": "tsc -b && vite build", "tokens:json": "node scripts/convert-tokens-json.js", "tokens:ts": "node scripts/convert-tokens-ts.js", "tokens:all": "npm run tokens:json && npm run tokens:ts"}, "keywords": [], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "ISC", "repository": {"type": "git", "url": "git+ssh://***********************:cjexpress/design-systems/apollo.git"}, "publishConfig": {"@apollo:registry": "https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/"}, "homepage": "https://gitlab.cjexpress.io/cjexpress/design-systems/apollo#readme", "dependencies": {"tailwindcss": "4.0.0", "deepmerge": "^4.3.1"}, "devDependencies": {"@tokens-studio/sd-transforms": "^0.12.2", "node-jq": "6.0.1", "sd-tailwindcss-transformer": "^1.4.1", "style-dictionary": "^3.9.1", "token-transformer": "0.0.33", "@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5", "vite-plugin-dts": "^4.5.0"}}