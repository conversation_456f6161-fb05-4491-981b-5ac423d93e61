import React from 'react'
import { NestedThemeProvider, useTheme, useIsNestedTheme, useThemeDepth, createNestedTheme, extendTheme } from './NestedThemeProvider'
import type { ApolloDesignTokenConfig } from './types'

// Example theme configurations
const darkTheme: Partial<ApolloDesignTokenConfig> = {
  'apl-colors-surface-static-default1': '#1a1a1a',
  'apl-colors-surface-static-default2': '#2a2a2a',
  'apl-colors-content-default': '#ffffff',
  'apl-colors-border-default': '#404040',
}

const primaryTheme: Partial<ApolloDesignTokenConfig> = {
  'apl-colors-content-primary-default': '#0066cc',
  'apl-colors-border-primary-default': '#0066cc',
  'apl-colors-surface-action-primary-default': '#0066cc',
}

const warningTheme: Partial<ApolloDesignTokenConfig> = {
  'apl-colors-content-warning-default': '#ff6600',
  'apl-colors-border-warning-default': '#ff6600',
  'apl-colors-surface-static-warning-default': '#fff3e0',
}

// Component to display theme information
function ThemeInfo() {
  const { theme, parentTheme, isNested, depth } = useTheme()
  
  return (
    <div style={{ 
      padding: '16px', 
      border: '1px solid var(--apl-colors-border-default)',
      backgroundColor: 'var(--apl-colors-surface-static-default1)',
      color: 'var(--apl-colors-content-default)',
      margin: '8px 0'
    }}>
      <h4>Theme Info</h4>
      <p>Is Nested: {isNested ? 'Yes' : 'No'}</p>
      <p>Depth: {depth}</p>
      <p>Theme Keys Count: {Object.keys(theme).length}</p>
      {parentTheme && <p>Parent Theme Keys Count: {Object.keys(parentTheme).length}</p>}
    </div>
  )
}

// Example component that uses theme variables
function ThemedCard({ children, title }: { children: React.ReactNode; title: string }) {
  return (
    <div style={{
      padding: '16px',
      backgroundColor: 'var(--apl-colors-surface-static-default2)',
      border: '1px solid var(--apl-colors-border-default)',
      borderRadius: '8px',
      color: 'var(--apl-colors-content-default)',
      margin: '8px 0'
    }}>
      <h3 style={{ 
        color: 'var(--apl-colors-content-primary-default)',
        marginTop: 0 
      }}>
        {title}
      </h3>
      {children}
    </div>
  )
}

// Example 1: Basic nested theme usage
export function BasicNestedThemeExample() {
  return (
    <NestedThemeProvider>
      <div style={{ padding: '20px' }}>
        <h2>Root Theme (Apollo Base)</h2>
        <ThemeInfo />
        <ThemedCard title="Root Level Card">
          This card uses the base Apollo theme from apolloTokens.
        </ThemedCard>

        <NestedThemeProvider theme={darkTheme} scope=".dark-section">
          <div style={{ padding: '20px' }}>
            <h2>Dark Theme (Nested)</h2>
            <ThemeInfo />
            <ThemedCard title="Dark Themed Card">
              This card inherits from the base theme but overrides with dark colors.
            </ThemedCard>

            <NestedThemeProvider theme={primaryTheme} scope=".primary-section">
              <div style={{ padding: '20px' }}>
                <h2>Primary Theme (Double Nested)</h2>
                <ThemeInfo />
                <ThemedCard title="Primary Themed Card">
                  This card inherits from dark theme and adds primary color overrides.
                </ThemedCard>
              </div>
            </NestedThemeProvider>
          </div>
        </NestedThemeProvider>
      </div>
    </NestedThemeProvider>
  )
}

// Example 2: Non-inheriting theme
export function NonInheritingThemeExample() {
  return (
    <NestedThemeProvider>
      <div style={{ padding: '20px' }}>
        <h2>Root Theme</h2>
        <ThemeInfo />
        <ThemedCard title="Root Card">Root theme content</ThemedCard>

        <NestedThemeProvider theme={warningTheme} inherit={false} scope=".warning-section">
          <div style={{ padding: '20px' }}>
            <h2>Warning Theme (Non-inheriting)</h2>
            <ThemeInfo />
            <ThemedCard title="Warning Card">
              This theme does not inherit from parent - it merges with base theme only.
            </ThemedCard>
          </div>
        </NestedThemeProvider>
      </div>
    </NestedThemeProvider>
  )
}

// Example 3: Using helper functions
export function HelperFunctionsExample() {
  // Create a theme using helper function
  const customTheme = createNestedTheme({
    'apl-colors-content-default': '#333333',
    'apl-colors-surface-static-default1': '#f5f5f5',
  })

  // Extend an existing theme
  const extendedDarkTheme = extendTheme(darkTheme, {
    'apl-colors-content-primary-default': '#66ccff',
  })

  return (
    <NestedThemeProvider>
      <div style={{ padding: '20px' }}>
        <h2>Helper Functions Example</h2>
        
        <NestedThemeProvider theme={customTheme} scope=".custom-section">
          <div style={{ padding: '20px' }}>
            <h3>Custom Theme (using createNestedTheme)</h3>
            <ThemeInfo />
            <ThemedCard title="Custom Card">
              Created using createNestedTheme helper function.
            </ThemedCard>
          </div>
        </NestedThemeProvider>

        <NestedThemeProvider theme={extendedDarkTheme} scope=".extended-section">
          <div style={{ padding: '20px' }}>
            <h3>Extended Dark Theme (using extendTheme)</h3>
            <ThemeInfo />
            <ThemedCard title="Extended Card">
              Created by extending dark theme with additional properties.
            </ThemedCard>
          </div>
        </NestedThemeProvider>
      </div>
    </NestedThemeProvider>
  )
}

// Example 4: Custom wrapper component
export function CustomWrapperExample() {
  return (
    <NestedThemeProvider>
      <div style={{ padding: '20px' }}>
        <h2>Custom Wrapper Example</h2>
        
        <NestedThemeProvider 
          theme={primaryTheme} 
          WrapperComponent="section"
          themeClassName="primary-section"
          style={{ border: '2px solid var(--apl-colors-border-primary-default)' }}
        >
          <h3>Section with Primary Theme</h3>
          <ThemeInfo />
          <ThemedCard title="Section Card">
            This content is wrapped in a section element with primary theme.
          </ThemedCard>
        </NestedThemeProvider>
      </div>
    </NestedThemeProvider>
  )
}

// Example 5: Conditional theme hooks
export function ConditionalThemeExample() {
  function ConditionalContent() {
    const isNested = useIsNestedTheme()
    const depth = useThemeDepth()
    
    return (
      <div style={{ padding: '16px' }}>
        {isNested ? (
          <p>This content knows it's in a nested theme at depth {depth}</p>
        ) : (
          <p>This content is in the root theme</p>
        )}
      </div>
    )
  }

  return (
    <NestedThemeProvider>
      <div style={{ padding: '20px' }}>
        <h2>Conditional Theme Hooks</h2>
        <ConditionalContent />
        
        <NestedThemeProvider theme={darkTheme}>
          <ConditionalContent />
          
          <NestedThemeProvider theme={primaryTheme}>
            <ConditionalContent />
          </NestedThemeProvider>
        </NestedThemeProvider>
      </div>
    </NestedThemeProvider>
  )
}
