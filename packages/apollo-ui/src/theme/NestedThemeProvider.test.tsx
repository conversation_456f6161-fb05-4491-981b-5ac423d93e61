import React from 'react'
import { NestedThemeProvider, useTheme, createNestedTheme, extendTheme } from './NestedThemeProvider'



describe('NestedThemeProvider', () => {
  it('should create themes using helper functions', () => {
    const customTheme = createNestedTheme({
      'apl-colors-content-default': '#333333',
    })

    expect(customTheme).toBeDefined()
    expect(typeof customTheme).toBe('object')
    expect(customTheme['apl-colors-content-default']).toBe('#333333')
  })

  it('should extend themes properly', () => {
    const baseTheme = { 'apl-colors-content-default': '#000000' }
    const extendedTheme = extendTheme(baseTheme, {
      'apl-colors-content-primary-default': '#0066cc',
    })

    expect(extendedTheme).toBeDefined()
    expect(extendedTheme['apl-colors-content-default']).toBe('#000000')
    expect(extendedTheme['apl-colors-content-primary-default']).toBe('#0066cc')
  })

  it('should render without errors', () => {
    const TestComponent = () => {
      const { theme, isNested, depth } = useTheme()

      return (
        <div>
          <span>Nested: {isNested.toString()}</span>
          <span>Depth: {depth}</span>
          <span>Theme keys: {Object.keys(theme).length}</span>
        </div>
      )
    }

    expect(() => {
      // This would normally use render from testing library
      // For now, just test that the component can be created
      React.createElement(NestedThemeProvider, { children: React.createElement(TestComponent) })
    }).not.toThrow()
  })

})
