import { HTMLElementType, useMemo, type ComponentType, createContext, useContext, type ReactNode } from "react"
import { apolloTokens } from "@apollo/token"

import { animationStyles } from "./override/animation"
import { baseline } from "./override/baseline"
import { reactDatePickerStyleOverride } from "./override/react-datepicker"
import { zIndexStyles } from "./override/zIndex"
import type { ApolloDesignTokenConfig } from "./types"
import { getInjectorId, getThemeWrapperIdentityId, parseToken, overrideExistedKey } from "./utils"

// Create a base theme from apolloTokens - use any to handle structural differences
const baseTheme: Partial<ApolloDesignTokenConfig> = apolloTokens.tokens as any

// Theme context for nested theme support
interface ThemeContextValue {
  theme: Partial<ApolloDesignTokenConfig>
  parentTheme?: Partial<ApolloDesignTokenConfig>
  isNested: boolean
  depth: number
}

const ThemeContext = createContext<ThemeContextValue | null>(null)

// Hook to access theme context
export function useTheme(): ThemeContextValue {
  const context = useContext(ThemeContext)
  return context || {
    theme: baseTheme,
    isNested: false,
    depth: 0
  }
}

// Hook to check if we're in a nested theme
export function useIsNestedTheme(): boolean {
  const context = useContext(ThemeContext)
  return context?.isNested || false
}

// Hook to get the current theme depth
export function useThemeDepth(): number {
  const context = useContext(ThemeContext)
  return context?.depth || 0
}

interface NestedThemeProviderProps {
  children: ReactNode
  theme?: Partial<ApolloDesignTokenConfig>
  scope?: string
  className?: string
  /**
   * Whether to inherit from parent theme (default: true)
   * When true, the theme will be merged with the parent theme
   * When false, the theme will completely override the parent theme
   */
  inherit?: boolean
  /**
   * Custom CSS class name for the theme wrapper
   */
  themeClassName?: string
  /**
   * Wrapper component - can be a string (HTML element) or React component
   */
  WrapperComponent?: HTMLElementType | ComponentType<any>
  /**
   * Additional props to pass to the wrapper component
   */
  [key: string]: any
}

export function NestedThemeProvider({
  children,
  scope,
  theme: propsTheme,
  WrapperComponent,
  inherit = true,
  themeClassName,
  ...wrapperProps
}: NestedThemeProviderProps) {
  const parentContext = useContext(ThemeContext)
  
  /**
   * Determine the effective theme based on inheritance and nesting
   */
  const theme = useMemo(() => {
    // If no theme provided, use base theme or parent theme
    if (!propsTheme) {
      return parentContext?.theme ?? baseTheme
    }

    // If we're nested and inherit is true, merge with parent theme
    if (parentContext && inherit) {
      return overrideExistedKey(propsTheme, parentContext.theme)
    }

    // If inherit is false or no parent, merge with base theme
    return overrideExistedKey(propsTheme, baseTheme)
  }, [propsTheme, parentContext, inherit])

  const overrideStyles = [
    reactDatePickerStyleOverride,
    animationStyles,
    zIndexStyles,
    baseline,
  ]

  const cssVariables = theme ? parseToken(theme) : ""
  const isNested = Boolean(parentContext)
  const depth = (parentContext?.depth ?? 0) + (propsTheme ? 1 : 0)
  
  // Generate unique scope for nested themes
  const effectiveScope = scope ?? (isNested ? `.nested-theme-${depth}` : ":root")
  const variableScope = effectiveScope

  const injectorId = `apl-${getInjectorId(variableScope)}`
  const wrapperId = getThemeWrapperIdentityId(injectorId)
  const hasWrapper = Boolean(scope) || isNested
  const cssSelector = hasWrapper
    ? `[data-apl="${wrapperId}"], ${effectiveScope}`
    : ":root"
  
  let Wrapper: HTMLElementType | ComponentType = "div" as HTMLElementType
  if (WrapperComponent) {
    if (typeof WrapperComponent === "string") {
      Wrapper = WrapperComponent as HTMLElementType
    } else {
      Wrapper = WrapperComponent as ComponentType
    }
  }

  // Create theme context value
  const themeContextValue: ThemeContextValue = useMemo(() => ({
    theme,
    parentTheme: parentContext?.theme,
    isNested,
    depth
  }), [theme, parentContext?.theme, isNested, depth])

  const wrapperClassName = [
    themeClassName,
    isNested ? `nested-theme nested-theme-depth-${depth}` : 'root-theme',
    (wrapperProps as any)?.className
  ].filter(Boolean).join(' ')

  return (
    <ThemeContext.Provider value={themeContextValue}>
      {/* Only inject font styles at root level to avoid duplication */}
      {!isNested && (
        <style
          dangerouslySetInnerHTML={{
            __html:
              "@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Thai:wght@100;200;300;400;500;600;700&display=swap');",
          }}
          data-apl={`${injectorId}-font-family`}
        />
      )}
      
      {/* Only inject override styles at root level to avoid duplication */}
      {!isNested && (
        <style
          dangerouslySetInnerHTML={{
            __html: overrideStyles?.join(""),
          }}
          data-apl={`${injectorId}-style-override`}
        />
      )}
      
      {/* Theme variables - always inject for each theme level */}
      <style
        dangerouslySetInnerHTML={{
          __html: `${cssSelector} {${cssVariables}}`,
        }}
        data-apl={`${injectorId}-theme`}
      />
      
      {hasWrapper ? (
        <Wrapper
          data-apl={wrapperId}
          {...wrapperProps}
          className={wrapperClassName}
        >
          {children}
        </Wrapper>
      ) : (
        children
      )}
    </ThemeContext.Provider>
  )
}

// Helper function to create a theme that extends the base theme
export function createNestedTheme(
  themeOverrides: Partial<ApolloDesignTokenConfig>
): Partial<ApolloDesignTokenConfig> {
  return overrideExistedKey(themeOverrides, baseTheme)
}

// Helper function to create a theme that extends a parent theme
export function extendTheme(
  parentTheme: Partial<ApolloDesignTokenConfig>,
  themeOverrides: Partial<ApolloDesignTokenConfig>
): Partial<ApolloDesignTokenConfig> {
  return overrideExistedKey(themeOverrides, parentTheme)
}
