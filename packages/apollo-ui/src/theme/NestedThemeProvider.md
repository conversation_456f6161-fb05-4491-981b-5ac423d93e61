# NestedThemeProvider

A new theme provider that uses baseTheme from apolloTokens and supports nested theme configurations.

## Features

- **Apollo Tokens Integration**: Uses `apolloTokens.tokens` as the base theme instead of the legacy `apolloTheme`
- **Nested Theme Support**: Allows multiple levels of theme nesting with proper inheritance
- **Theme Inheritance Control**: Choose whether nested themes inherit from parent or base theme
- **Context Hooks**: Access theme information and nesting state from any component
- **Performance Optimized**: Avoids duplicate style injection for nested themes
- **TypeScript Support**: Full type safety with Apollo design token types

## Basic Usage

```tsx
import { NestedThemeProvider } from '@apollo/ui'

function App() {
  return (
    <NestedThemeProvider>
      <YourAppContent />
    </NestedThemeProvider>
  )
}
```

## Nested Themes

```tsx
const darkTheme = {
  'apl-colors-surface-static-default1': '#1a1a1a',
  'apl-colors-content-default': '#ffffff',
}

const primaryTheme = {
  'apl-colors-content-primary-default': '#0066cc',
}

function NestedExample() {
  return (
    <NestedThemeProvider>
      {/* Root theme uses apolloTokens.tokens */}
      <div>Root content</div>
      
      <NestedThemeProvider theme={darkTheme} scope=".dark-section">
        {/* Inherits from root + dark overrides */}
        <div>Dark themed content</div>
        
        <NestedThemeProvider theme={primaryTheme} scope=".primary-section">
          {/* Inherits from dark + primary overrides */}
          <div>Primary themed content</div>
        </NestedThemeProvider>
      </NestedThemeProvider>
    </NestedThemeProvider>
  )
}
```

## Props

### NestedThemeProviderProps

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `children` | `ReactNode` | - | Child components |
| `theme` | `Partial<ApolloDesignTokenConfig>` | - | Theme overrides |
| `scope` | `string` | - | CSS selector scope for the theme |
| `inherit` | `boolean` | `true` | Whether to inherit from parent theme |
| `themeClassName` | `string` | - | Custom CSS class for theme wrapper |
| `WrapperComponent` | `ComponentType \| HTMLElementType` | `"div"` | Wrapper component |

## Hooks

### useTheme()

Returns the current theme context information.

```tsx
import { useTheme } from '@apollo/ui'

function MyComponent() {
  const { theme, parentTheme, isNested, depth } = useTheme()
  
  return (
    <div>
      <p>Current depth: {depth}</p>
      <p>Is nested: {isNested}</p>
      <p>Theme keys: {Object.keys(theme).length}</p>
    </div>
  )
}
```

### useIsNestedTheme()

Returns whether the component is inside a nested theme.

```tsx
import { useIsNestedTheme } from '@apollo/ui'

function ConditionalComponent() {
  const isNested = useIsNestedTheme()
  
  return isNested ? <NestedContent /> : <RootContent />
}
```

### useThemeDepth()

Returns the current nesting depth (0 for root theme).

```tsx
import { useThemeDepth } from '@apollo/ui'

function DepthIndicator() {
  const depth = useThemeDepth()
  
  return <div className={`theme-depth-${depth}`}>Depth: {depth}</div>
}
```

## Helper Functions

### createNestedTheme(overrides)

Creates a theme by merging overrides with the base apolloTokens theme.

```tsx
import { createNestedTheme } from '@apollo/ui'

const myTheme = createNestedTheme({
  'apl-colors-content-default': '#333333',
  'apl-colors-surface-static-default1': '#f5f5f5',
})
```

### extendTheme(parentTheme, overrides)

Creates a theme by extending a parent theme with additional overrides.

```tsx
import { extendTheme } from '@apollo/ui'

const baseTheme = { /* ... */ }
const extendedTheme = extendTheme(baseTheme, {
  'apl-colors-content-primary-default': '#0066cc',
})
```

## Advanced Usage

### Non-inheriting Themes

Set `inherit={false}` to prevent inheritance from parent themes:

```tsx
<NestedThemeProvider theme={independentTheme} inherit={false}>
  {/* This theme only merges with base apolloTokens, not parent */}
</NestedThemeProvider>
```

### Custom Wrapper Components

Use custom wrapper components for semantic HTML:

```tsx
<NestedThemeProvider 
  theme={sectionTheme}
  WrapperComponent="section"
  themeClassName="themed-section"
  aria-label="Themed section"
>
  <SectionContent />
</NestedThemeProvider>
```

### Scoped Themes

Use CSS selectors to scope themes to specific areas:

```tsx
<NestedThemeProvider theme={modalTheme} scope=".modal">
  {/* Theme only applies to elements with .modal class */}
</NestedThemeProvider>
```

## Migration from ThemeProvider

The NestedThemeProvider is designed to be a drop-in replacement for the existing ThemeProvider with additional features:

```tsx
// Old
<ThemeProvider theme={myTheme}>
  <App />
</ThemeProvider>

// New
<NestedThemeProvider theme={myTheme}>
  <App />
</NestedThemeProvider>
```

Key differences:
- Uses `apolloTokens.tokens` as base theme instead of `apolloTheme`
- Supports nesting with inheritance control
- Provides context hooks for theme information
- Optimizes style injection for nested scenarios

## Performance Considerations

- Font imports and global styles are only injected at the root level
- Each nested theme only injects its specific CSS variables
- Theme merging is memoized to prevent unnecessary recalculations
- CSS selectors are optimized for nested theme scenarios

## TypeScript Support

All components and hooks are fully typed with Apollo design token types:

```tsx
import type { ApolloDesignTokenConfig } from '@apollo/ui'

const typedTheme: Partial<ApolloDesignTokenConfig> = {
  'apl-colors-content-default': '#000000', // ✅ Valid token
  'invalid-token': '#ffffff', // ❌ TypeScript error
}
```
